import { Injectable } from '@nestjs/common';

import { DatabaseFactory } from '@/core/database/database.factory';
import { BaseBusinessService } from '@/modules/business/base/business.service';

@Injectable()
export class ComponentLibraryService extends BaseBusinessService {
  constructor(databaseFactory: DatabaseFactory) {
    super(databaseFactory, 'ComponentLibraryService');
  }

  // TODO: 实现组件库功能
  async getComponents() {
    throw new Error('Method not implemented - Schema needs to be verified');
  }
}
