import { ApiProperty } from '@nestjs/swagger';
import { JsonValue } from '@prisma/client/runtime/library';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsBoolean, IsInt, IsObject, IsDate } from 'class-validator';

export class WebsitePageEntity {
  @ApiProperty({ description: '页面ID' })
  id: number;

  @ApiProperty({ description: '页面标题' })
  @IsString()
  title: string;

  @ApiProperty({ description: '页面路径/别名' })
  @IsString()
  slug: string;

  @ApiProperty({ description: '页面内容/组件配置', type: 'object' })
  @IsObject()
  content: JsonValue;

  @ApiProperty({
    description: '页面类型',
    examples: ['page', 'home', 'about', 'contact', 'blog'],
    default: 'page',
  })
  @IsString()
  type: string;

  @ApiProperty({
    description: '页面状态',
    enum: ['draft', 'published'],
    default: 'draft',
  })
  @IsString()
  status: string;

  @ApiProperty({ description: 'SEO标题', required: false })
  @IsOptional()
  @IsString()
  seoTitle?: string;

  @ApiProperty({ description: 'SEO描述', required: false })
  @IsOptional()
  @IsString()
  seoDescription?: string;

  @ApiProperty({ description: 'SEO关键词', required: false })
  @IsOptional()
  @IsString()
  seoKeywords?: string;

  @ApiProperty({ description: '自定义CSS', required: false })
  @IsOptional()
  @IsString()
  customCSS?: string;

  @ApiProperty({ description: '自定义JavaScript', required: false })
  @IsOptional()
  @IsString()
  customJS?: string;

  @ApiProperty({ description: '排序权重', default: 0 })
  @IsInt()
  sortOrder: number;

  @ApiProperty({ description: '是否为首页', default: false })
  @IsBoolean()
  isHomePage: boolean;

  @ApiProperty({ description: '元数据', type: 'object', required: false })
  @IsOptional()
  @IsObject()
  metadata?: JsonValue;

  @ApiProperty({ description: '所属网站ID' })
  websiteId: number;

  @ApiProperty({ description: '租户ID' })
  tenantId: number;

  @ApiProperty({ description: '创建时间' })
  @IsDate()
  @Type(() => Date)
  createTime: Date;

  @ApiProperty({ description: '更新时间' })
  @IsDate()
  @Type(() => Date)
  updateTime: Date;

  // 关联对象（可选）
  @ApiProperty({ description: '所属网站信息', required: false })
  @IsOptional()
  website?: any;
}
