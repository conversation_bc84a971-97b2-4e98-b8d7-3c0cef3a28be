import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsNumber,
  IsEnum,
  IsOptional,
  IsObject,
  Min,
  IsDateString,
  IsIn,
} from 'class-validator';

/**
 * 支付状态枚举
 */
export enum PaymentStatus {
  PENDING = 'pending',
  SUCCESS = 'success',
  FAILED = 'failed',
  REFUNDED = 'refunded',
}

/**
 * 支付方式枚举
 */
export enum PaymentMethod {
  ALIPAY = 'alipay',
  WECHAT = 'wechat',
  CREDITCARD = 'creditcard',
}

/**
 * 创建支付订单DTO
 */
export class CreatePaymentDto {
  @ApiProperty({ description: '业务类型', example: 'membership' })
  @IsString()
  businessType: string;

  @ApiProperty({ description: '业务ID', example: '1' })
  @IsString()
  businessId: string;

  @ApiProperty({ description: '支付金额', example: 299.0 })
  @IsNumber()
  @Min(0.01)
  amount: number;

  @ApiProperty({ description: '支付方式', enum: PaymentMethod, example: PaymentMethod.ALIPAY })
  @IsEnum(PaymentMethod)
  paymentMethod: PaymentMethod;

  @ApiProperty({ description: '支付主题', example: '会员订阅-高级版' })
  @IsString()
  subject: string;

  @ApiPropertyOptional({ description: '支付描述', example: '购买3个月高级会员' })
  @IsString()
  @IsOptional()
  description?: string;

  @ApiPropertyOptional({
    description: '元数据',
    type: 'object',
    example: { planId: 1, duration: 3, billingCycle: 'monthly' },
  })
  @IsObject()
  @IsOptional()
  metadata?: Record<string, any>;
}

/**
 * 支付结果DTO
 */
export class PaymentResultDto {
  @ApiProperty({ description: '订单号', example: 'P2305010001' })
  orderNo: string;

  @ApiProperty({ description: '支付金额', example: 299.0 })
  amount: number;

  @ApiProperty({
    description: '支付链接',
    example: 'https://payment-gateway.com/pay?orderNo=P2305010001',
  })
  paymentUrl: string;

  @ApiProperty({ description: '支付二维码', example: 'base64-encoded-qr-code-image' })
  qrCode: string;
}

/**
 * 支付订单响应DTO
 */
export class PaymentResponseDto {
  @ApiProperty({ description: '订单ID', example: 1 })
  id: number;

  @ApiProperty({ description: '订单号', example: 'P2305010001' })
  orderNo: string;

  @ApiProperty({ description: '用户ID', example: 1 })
  userId: number;

  @ApiPropertyOptional({ description: '用户名', example: 'user123' })
  username?: string;

  @ApiProperty({ description: '支付金额', example: 299.0 })
  amount: number;

  @ApiProperty({ description: '支付状态', enum: PaymentStatus, example: PaymentStatus.SUCCESS })
  status: PaymentStatus;

  @ApiProperty({ description: '支付方式', enum: PaymentMethod, example: PaymentMethod.ALIPAY })
  paymentMethod: PaymentMethod;

  @ApiProperty({ description: '业务类型', example: 'membership' })
  businessType: string;

  @ApiProperty({ description: '业务ID', example: '1' })
  businessId: string;

  @ApiProperty({ description: '支付主题', example: '会员订阅-高级版' })
  subject: string;

  @ApiProperty({ description: '创建时间', example: '2023-05-01T10:30:00Z' })
  createTime: Date;

  @ApiPropertyOptional({ description: '支付时间', example: '2023-05-01T10:35:00Z' })
  paymentTime?: Date;

  @ApiPropertyOptional({
    description: '元数据',
    type: 'object',
    example: { planId: 1, duration: 3, billingCycle: 'monthly' },
  })
  metadata?: Record<string, any>;
}

/**
 * 查询支付订单DTO
 */
export class QueryPaymentDto {
  @ApiPropertyOptional({ description: '业务类型', example: 'membership' })
  @IsString()
  @IsOptional()
  businessType?: string;

  @ApiPropertyOptional({ description: '支付状态', enum: PaymentStatus })
  @IsEnum(PaymentStatus)
  @IsOptional()
  status?: PaymentStatus;

  @ApiPropertyOptional({ description: '开始时间', example: '2023-05-01T00:00:00Z' })
  @IsDateString()
  @IsOptional()
  startTime?: string;

  @ApiPropertyOptional({ description: '结束时间', example: '2023-05-31T23:59:59Z' })
  @IsDateString()
  @IsOptional()
  endTime?: string;

  @ApiPropertyOptional({ description: '页码', example: 1 })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  page?: number;

  @ApiPropertyOptional({ description: '每页数量', example: 10 })
  @IsNumber()
  @Type(() => Number)
  @IsOptional()
  pageSize?: number;
}

/**
 * 退款申请DTO
 */
export class RefundRequestDto {
  @ApiProperty({ description: '订单号', example: 'P2305010001' })
  @IsString()
  orderNo: string;

  @ApiProperty({ description: '退款金额', example: 299.0 })
  @IsNumber()
  @Min(0.01)
  amount: number;

  @ApiProperty({ description: '退款原因', example: '不想要了' })
  @IsString()
  reason: string;
}

/**
 * 处理退款DTO
 */
export class ProcessRefundDto {
  @ApiProperty({ description: '退款单号', example: 'R2305010001' })
  @IsString()
  refundNo: string;

  @ApiProperty({ description: '处理动作', example: 'approve' })
  @IsString()
  @IsIn(['approve', 'reject'])
  action: 'approve' | 'reject';

  @ApiPropertyOptional({ description: '处理备注', example: '同意退款' })
  @IsString()
  @IsOptional()
  comment?: string;
}

/**
 * 支付统计DTO
 */
export class PaymentStatsDto {
  @ApiProperty({ description: '总收入', example: 9999.0 })
  totalRevenue: number;

  @ApiProperty({ description: '成功订单数', example: 100 })
  successOrders: number;

  @ApiProperty({ description: '待支付订单数', example: 20 })
  pendingOrders: number;

  @ApiProperty({ description: '失败订单数', example: 5 })
  failedOrders: number;

  @ApiProperty({ description: '退款金额', example: 299.0 })
  refundedAmount: number;

  @ApiProperty({
    description: '支付方式分布',
    type: [Object],
    example: [
      { method: 'alipay', count: 50, amount: 5000, percentage: 50 },
      { method: 'wechat', count: 40, amount: 4000, percentage: 40 },
      { method: 'creditcard', count: 10, amount: 999, percentage: 10 },
    ],
  })
  paymentMethodDistribution: Array<{
    method: string;
    count: number;
    amount: number;
    percentage: number;
  }>;

  @ApiProperty({
    description: '业务类型分布',
    type: [Object],
    example: [
      { type: 'membership', count: 70, amount: 7000, percentage: 70 },
      { type: 'virtual-currency', count: 30, amount: 2999, percentage: 30 },
    ],
  })
  businessTypeDistribution: Array<{
    type: string;
    count: number;
    amount: number;
    percentage: number;
  }>;

  @ApiProperty({
    description: '每日收入',
    type: [Object],
    example: [
      { date: '2023-05-01', amount: 1000 },
      { date: '2023-05-02', amount: 1200 },
      { date: '2023-05-03', amount: 800 },
    ],
  })
  dailyRevenue: Array<{
    date: string;
    amount: number;
  }>;
}
