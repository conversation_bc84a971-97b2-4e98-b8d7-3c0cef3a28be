import { Injectable, NotFoundException, Inject } from '@nestjs/common';

import { GenerateSitemapDto } from './dto/generate-sitemap.dto';
import { SitemapEntity } from './entities/sitemap.entity';

import { DatabaseFactory } from '@/core/database/database.factory';
import { BaseBusinessService } from '@/modules/business/base/business.service';

@Injectable()
export class SitemapService extends BaseBusinessService {
  constructor(databaseFactory: DatabaseFactory) {
    super(databaseFactory, 'SitemapService');
  }

  /**
   * 生成站点地图
   */
  async generateSitemap(tenantId: number, generateDto: GenerateSitemapDto): Promise<SitemapEntity> {
    const {
      websiteId,
      type = 'xml',
      includeImages = true,
      includeVideos = false,
      includeNews = false,
      maxUrls = 50000,
    } = generateDto;

    // 验证网站是否存在
    const website = await this.tenantDb.website.findFirst({
      where: {
        id: websiteId,
        tenantId,
      },
      include: {
        pages: {
          where: {
            status: 'published',
          },
        },
      },
    });

    if (!website) {
      throw new NotFoundException('网站不存在');
    }

    // 生成站点地图内容
    const content = await this.generateSitemapContent(
      website,
      type,
      includeImages,
      includeVideos,
      includeNews,
      maxUrls,
    );

    // 生成站点地图URL
    const baseUrl = website.domain || `${website.subdomain}.example.com`;
    const url = `https://${baseUrl}/sitemap.${type}`;

    // 检查是否已存在相同类型的站点地图
    const existingSitemap = await this.tenantDb.sitemap.findFirst({
      where: {
        websiteId,
        type,
        tenantId,
      },
    });

    let sitemap;
    if (existingSitemap) {
      // 更新现有站点地图
      sitemap = await this.tenantDb.sitemap.update({
        where: {
          id: existingSitemap.id,
        },
        data: {
          content,
          url,
          lastGenerated: new Date(),
        },
        include: {
          website: true,
        },
      });
    } else {
      // 创建新的站点地图
      sitemap = await this.tenantDb.sitemap.create({
        data: {
          websiteId,
          type,
          url,
          content,
          lastGenerated: new Date(),
          isActive: true,
          tenantId,
        },
        include: {
          website: true,
        },
      });
    }

    return this.mapToEntity(sitemap);
  }

  /**
   * 根据网站ID获取所有站点地图
   */
  async findByWebsiteId(tenantId: number, websiteId: number): Promise<SitemapEntity[]> {
    const sitemaps = await this.tenantDb.sitemap.findMany({
      where: {
        websiteId,
        tenantId,
      },
      include: {
        website: true,
      },
      orderBy: {
        lastGenerated: 'desc',
      },
    });

    return sitemaps.map(sitemap => this.mapToEntity(sitemap));
  }

  /**
   * 获取单个站点地图详情
   */
  async findOne(tenantId: number, id: number): Promise<SitemapEntity> {
    const sitemap = await this.tenantDb.sitemap.findFirst({
      where: {
        id,
        tenantId,
      },
      include: {
        website: true,
      },
    });

    if (!sitemap) {
      throw new NotFoundException('站点地图不存在');
    }

    return this.mapToEntity(sitemap);
  }

  /**
   * 切换站点地图激活状态
   */
  async toggleActive(tenantId: number, id: number): Promise<SitemapEntity> {
    const sitemap = await this.tenantDb.sitemap.findFirst({
      where: {
        id,
        tenantId,
      },
    });

    if (!sitemap) {
      throw new NotFoundException('站点地图不存在');
    }

    const updatedSitemap = await this.tenantDb.sitemap.update({
      where: {
        id,
      },
      data: {
        isActive: !sitemap.isActive,
      },
      include: {
        website: true,
      },
    });

    return this.mapToEntity(updatedSitemap);
  }

  /**
   * 删除站点地图
   */
  async remove(tenantId: number, id: number): Promise<void> {
    const sitemap = await this.tenantDb.sitemap.findFirst({
      where: {
        id,
        tenantId,
      },
    });

    if (!sitemap) {
      throw new NotFoundException('站点地图不存在');
    }

    await this.tenantDb.sitemap.delete({
      where: {
        id,
      },
    });
  }

  /**
   * 生成站点地图内容
   */
  private async generateSitemapContent(
    website: any,
    type: string,
    includeImages: boolean,
    includeVideos: boolean,
    includeNews: boolean,
    maxUrls: number,
  ): Promise<string> {
    const baseUrl = website.domain || `${website.subdomain}.example.com`;
    const pages = website.pages.slice(0, maxUrls);

    switch (type) {
      case 'xml':
        return this.generateXmlSitemap(baseUrl, pages, includeImages, includeVideos, includeNews);
      case 'html':
        return this.generateHtmlSitemap(baseUrl, pages);
      case 'txt':
        return this.generateTxtSitemap(baseUrl, pages);
      default:
        return this.generateXmlSitemap(baseUrl, pages, includeImages, includeVideos, includeNews);
    }
  }

  /**
   * 生成XML格式站点地图
   */
  private generateXmlSitemap(
    baseUrl: string,
    pages: any[],
    includeImages: boolean,
    includeVideos: boolean,
    includeNews: boolean,
  ): string {
    let xml = '<?xml version="1.0" encoding="UTF-8"?>\n';
    xml += '<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9"';

    if (includeImages) {
      xml += ' xmlns:image="http://www.google.com/schemas/sitemap-image/1.1"';
    }
    if (includeVideos) {
      xml += ' xmlns:video="http://www.google.com/schemas/sitemap-video/1.1"';
    }
    if (includeNews) {
      xml += ' xmlns:news="http://www.google.com/schemas/sitemap-news/0.9"';
    }

    xml += '>\n';

    for (const page of pages) {
      xml += '  <url>\n';
      xml += `    <loc>https://${baseUrl}${page.slug}</loc>\n`;
      xml += `    <lastmod>${page.updateTime.toISOString().split('T')[0]}</lastmod>\n`;
      xml += `    <changefreq>weekly</changefreq>\n`;
      xml += `    <priority>${page.slug === '/' ? '1.0' : '0.8'}</priority>\n`;

      // 添加图片信息
      if (includeImages && page.content) {
        const images = this.extractImagesFromContent(page.content);
        for (const image of images) {
          xml += '    <image:image>\n';
          xml += `      <image:loc>${image}</image:loc>\n`;
          xml += '    </image:image>\n';
        }
      }

      xml += '  </url>\n';
    }

    xml += '</urlset>';
    return xml;
  }

  /**
   * 生成HTML格式站点地图
   */
  private generateHtmlSitemap(baseUrl: string, pages: any[]): string {
    let html = '<!DOCTYPE html>\n';
    html += '<html lang="zh-CN">\n';
    html += '<head>\n';
    html += '  <meta charset="UTF-8">\n';
    html += '  <meta name="viewport" content="width=device-width, initial-scale=1.0">\n';
    html += '  <title>网站地图</title>\n';
    html += '  <style>\n';
    html += '    body { font-family: Arial, sans-serif; margin: 40px; }\n';
    html += '    h1 { color: #333; }\n';
    html += '    ul { list-style-type: none; padding: 0; }\n';
    html += '    li { margin: 10px 0; }\n';
    html += '    a { color: #0066cc; text-decoration: none; }\n';
    html += '    a:hover { text-decoration: underline; }\n';
    html += '    .last-modified { color: #666; font-size: 0.9em; }\n';
    html += '  </style>\n';
    html += '</head>\n';
    html += '<body>\n';
    html += '  <h1>网站地图</h1>\n';
    html += '  <ul>\n';

    for (const page of pages) {
      const pageTitle = page.seoTitle || page.title || page.slug;
      html += '    <li>\n';
      html += `      <a href="https://${baseUrl}${page.slug}">${pageTitle}</a>\n`;
      html += `      <div class="last-modified">最后更新: ${page.updateTime.toISOString().split('T')[0]}</div>\n`;
      html += '    </li>\n';
    }

    html += '  </ul>\n';
    html += '</body>\n';
    html += '</html>';
    return html;
  }

  /**
   * 生成TXT格式站点地图
   */
  private generateTxtSitemap(baseUrl: string, pages: any[]): string {
    let txt = '';
    for (const page of pages) {
      txt += `https://${baseUrl}${page.slug}\n`;
    }
    return txt;
  }

  /**
   * 从页面内容中提取图片URL
   */
  private extractImagesFromContent(content: any): string[] {
    const images: string[] = [];

    try {
      if (typeof content === 'string') {
        // 如果content是字符串，尝试解析为JSON
        content = JSON.parse(content);
      }

      if (Array.isArray(content)) {
        // 如果是组件数组
        for (const component of content) {
          if (component.type === 'image' && component.props?.src) {
            images.push(component.props.src);
          }
          if (component.props?.images && Array.isArray(component.props.images)) {
            images.push(...component.props.images);
          }
        }
      }
    } catch (error) {
      // 如果解析失败，使用正则表达式提取图片URL
      const imgRegex = /<img[^>]+src="([^">]+)"/g;
      let match;
      while ((match = imgRegex.exec(content)) !== null) {
        images.push(match[1]);
      }
    }

    return images;
  }

  /**
   * 将数据库对象映射为实体
   */
  private mapToEntity(sitemap: any): SitemapEntity {
    return {
      id: sitemap.id,
      websiteId: sitemap.websiteId,
      type: sitemap.type,
      url: sitemap.url,
      content: sitemap.content,
      lastGenerated: sitemap.lastGenerated,
      isActive: sitemap.isActive,
      tenantId: sitemap.tenantId,
      createTime: sitemap.createTime,
      updateTime: sitemap.updateTime,
      website: sitemap.website,
    };
  }
}
