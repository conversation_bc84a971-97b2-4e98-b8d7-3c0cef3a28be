import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import type { Tenant, TenantDatasource } from '@prisma-public/prisma/client';

import { BasePlatformService } from '../base/platform.service';

import { DatabaseFactory } from '@/core/database/database.factory';

/**
 * 平台租户管理服务
 *
 * 负责平台级别的租户管理功能：
 * - 租户创建、更新、删除
 * - 租户配置管理
 * - 租户状态管理
 */
@Injectable()
export class TenantManagementService extends BasePlatformService {
  constructor(databaseFactory: DatabaseFactory) {
    super(databaseFactory, TenantManagementService.name);
  }

  /**
   * 获取所有租户列表
   */
  async findAll() {
    this.logOperation('获取租户列表');

    try {
      const tenants = await this.publicDb.tenant.findMany({
        include: {
          datasource: true,
          subscriptions: {
            where: { status: 'active' },
            include: { plan: true },
            orderBy: { createdAt: 'desc' },
            take: 1,
          },
          _count: {
            select: {
              features: { where: { enabled: true } },
              configs: true,
            },
          },
        },
        orderBy: { createTime: 'desc' },
      });

      return {
        data: tenants,
        total: tenants.length,
      };
    } catch (error) {
      this.logError('获取租户列表', error as Error);
      throw error;
    }
  }

  /**
   * 根据ID获取租户详情
   */
  async findOne(id: number) {
    this.logOperation('获取租户详情', { id });

    try {
      const tenant = await this.publicDb.tenant.findUnique({
        where: { id },
        include: {
          datasource: true,
          subscriptions: {
            include: { plan: true },
            orderBy: { createdAt: 'desc' },
          },
          features: {
            orderBy: { featureCode: 'asc' },
          },
          configs: {
            orderBy: { category: 'asc' },
          },
        },
      });

      if (!tenant) {
        throw new NotFoundException(`租户 ${id} 不存在`);
      }

      return tenant;
    } catch (error) {
      this.logError('获取租户详情', error as Error);
      throw error;
    }
  }

  /**
   * 创建新租户
   */
  async create(createData: {
    code: string;
    name: string;
    website?: string;
    domain?: string;
    isShared?: boolean;
    datasourceConfig?: {
      host?: string;
      port?: number;
      database?: string;
      username?: string;
      password?: string;
    };
  }) {
    this.logOperation('创建租户', { code: createData.code });

    try {
      // 检查租户代码是否已存在
      const existingTenant = await this.publicDb.tenant.findUnique({
        where: { code: createData.code },
      });

      if (existingTenant) {
        throw new BadRequestException(`租户代码 ${createData.code} 已存在`);
      }

      // 检查域名是否已被使用
      if (createData.domain) {
        const existingDomain = await this.publicDb.tenant.findFirst({
          where: { domain: createData.domain },
        });

        if (existingDomain) {
          throw new BadRequestException(`域名 ${createData.domain} 已被使用`);
        }
      }

      // 创建租户和数据源
      const result = await this.publicDb.$client.$transaction(async tx => {
        // 创建租户
        const tenant = await tx.tenant.create({
          data: {
            code: createData.code,
            name: createData.name,
            website: createData.website,
            domain: createData.domain,
            status: 1,
          },
        });

        // 创建数据源配置
        const datasource = await tx.tenantDatasource.create({
          data: {
            tenantId: tenant.id,
            isShared: createData.isShared ?? true,
            host: createData.datasourceConfig?.host,
            port: createData.datasourceConfig?.port,
            database: createData.datasourceConfig?.database,
            username: createData.datasourceConfig?.username,
            password: createData.datasourceConfig?.password,
          },
        });

        return { tenant, datasource };
      });

      this.logOperation('租户创建成功', {
        tenantId: result.tenant.id,
        code: result.tenant.code,
      });

      return result.tenant;
    } catch (error) {
      this.logError('创建租户', error as Error);
      throw error;
    }
  }

  /**
   * 更新租户信息
   */
  async update(
    id: number,
    updateData: {
      name?: string;
      website?: string;
      domain?: string;
      status?: number;
    },
  ) {
    this.logOperation('更新租户', { id, ...updateData });

    try {
      // 验证租户存在
      const existingTenant = await this.findOne(id);

      // 检查域名冲突
      if (updateData.domain && updateData.domain !== existingTenant.domain) {
        const domainConflict = await this.publicDb.tenant.findFirst({
          where: {
            domain: updateData.domain,
            id: { not: id },
          },
        });

        if (domainConflict) {
          throw new BadRequestException(`域名 ${updateData.domain} 已被使用`);
        }
      }

      const updatedTenant = await this.publicDb.tenant.update({
        where: { id },
        data: updateData,
      });

      this.logOperation('租户更新成功', { id });

      return updatedTenant;
    } catch (error) {
      this.logError('更新租户', error as Error);
      throw error;
    }
  }

  /**
   * 删除租户
   */
  async remove(id: number) {
    this.logOperation('删除租户', { id });

    try {
      // 验证租户存在
      await this.findOne(id);

      // 删除租户（级联删除相关数据）
      const deletedTenant = await this.publicDb.tenant.delete({
        where: { id },
      });

      this.logOperation('租户删除成功', { id });

      return deletedTenant;
    } catch (error) {
      this.logError('删除租户', error as Error);
      throw error;
    }
  }

  /**
   * 获取租户统计信息
   */
  async getStatistics() {
    this.logOperation('获取租户统计');

    try {
      const stats = await this.publicDb.$client.$transaction(async tx => {
        const total = await tx.tenant.count();
        const active = await tx.tenant.count({ where: { status: 1 } });
        const inactive = await tx.tenant.count({ where: { status: 0 } });
        const withDomain = await tx.tenant.count({
          where: { domain: { not: null } },
        });

        return {
          total,
          active,
          inactive,
          withDomain,
          sharedDatabase: await tx.tenantDatasource.count({
            where: { isShared: true },
          }),
          dedicatedDatabase: await tx.tenantDatasource.count({
            where: { isShared: false },
          }),
        };
      });

      return stats;
    } catch (error) {
      this.logError('获取租户统计', error as Error);
      throw error;
    }
  }
}
