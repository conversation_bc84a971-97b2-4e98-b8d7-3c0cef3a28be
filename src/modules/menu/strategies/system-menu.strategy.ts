import { Injectable, Logger, NotFoundException, ConflictException } from '@nestjs/common';

import { MenuStrategyType } from './menu-strategy.factory';
import { IMenuStrategy } from './menu-strategy.interface';
import { CreateMenuDto, MenuDto, MenuTreeDto, UpdateMenuDto } from '../dto/menu.dto';

import { ApiCode, ApiMessage } from '@/core/common/constants/api-code.constant';
import { DateFormatUtil } from '@/core/common/utils/date-format.util';
import { DatabaseFactory } from '@/core/database/database.factory';
/**
 * 系统菜单策略
 * 实现系统菜单相关的操作
 */
@Injectable()
export class SystemMenuStrategy implements IMenuStrategy {
  private readonly logger = new Logger(SystemMenuStrategy.name);

  constructor(private readonly prisma: PublicPrismaService) {}

  /**
   * 获取策略类型
   * @returns 策略类型
   */
  getType(): string {
    return MenuStrategyType.SYSTEM;
  }

  /**
   * 创建系统菜单
   * @param createMenuDto 创建菜单数据
   * @returns 创建的系统菜单
   */
  async create(createMenuDto: CreateMenuDto): Promise<MenuDto> {
    const { name, path, meta, ...rest } = createMenuDto;

    // 检查路径是否已存在
    const existingPath = await this.prisma.systemMenu.findFirst({
      where: {
        path,
      },
    });

    if (existingPath) {
      throw new ConflictException(`菜单路径 ${path} 已存在`);
    }

    // 创建菜单
    const menu = await this.prisma.systemMenu.create({
      data: {
        name,
        path,
        meta: meta ? JSON.stringify(meta) : '{}',
        ...rest,
      },
    });

    this.logger.log(`创建系统菜单: ${name}`);

    // 转换返回结果
    return this.transformMenuData(menu);
  }

  /**
   * 获取所有系统菜单
   * @returns 菜单列表
   */
  async findAll(): Promise<MenuDto[]> {
    const menus = await this.prisma.systemMenu.findMany({
      orderBy: { orderNo: 'asc' },
    });

    return this.buildMenuTree(menus);
  }

  /**
   * 获取系统菜单树
   * @returns 菜单树
   */
  async getMenuTree(): Promise<MenuTreeDto[]> {
    console.log('系统菜单策略 - 获取系统菜单树');
    this.logger.log('获取系统菜单树');

    try {
      console.log('系统菜单策略 - 查询系统菜单数据');
      this.logger.log('查询系统菜单数据');

      // 检查数据库连接
      console.log('系统菜单策略 - 检查数据库连接');
      if (!this.prisma) {
        console.error('系统菜单策略 - 数据库连接不可用');
        throw new Error('数据库连接不可用');
      }

      // 查询菜单数据
      console.log('系统菜单策略 - 执行查询: systemMenu.findMany');
      const menus = await this.prisma.systemMenu.findMany({
        where: { status: 1 },
        orderBy: { orderNo: 'asc' },
      });

      console.log(`系统菜单策略 - 查询到 ${menus.length} 条系统菜单数据`);
      this.logger.log(`查询到 ${menus.length} 条系统菜单数据`);

      if (menus.length === 0) {
        console.warn('系统菜单策略 - 系统菜单表中没有数据');
        this.logger.warn('系统菜单表中没有数据');
        return [];
      }

      // 记录菜单数据用于调试
      console.log(
        `系统菜单策略 - 菜单数据: ${JSON.stringify(menus.map(m => ({ id: m.id, name: m.name, path: m.path })))}`,
      );

      // 记录第一条菜单数据用于调试
      if (menus.length > 0) {
        const firstMenu = menus[0];
        console.log(
          `系统菜单策略 - 第一条菜单数据: ID=${firstMenu.id}, 名称=${firstMenu.name}, 路径=${firstMenu.path}`,
        );
        this.logger.log(
          `第一条菜单数据: ID=${firstMenu.id}, 名称=${firstMenu.name}, 路径=${firstMenu.path}`,
        );
      }

      console.log('系统菜单策略 - 开始构建前端菜单树');
      this.logger.log('开始构建前端菜单树');
      const menuTree = this.buildFrontendMenuTree(menus);
      console.log(`系统菜单策略 - 构建完成，菜单树包含 ${menuTree.length} 个顶级菜单`);
      this.logger.log(`构建完成，菜单树包含 ${menuTree.length} 个顶级菜单`);

      // 记录菜单树数据用于调试
      console.log(
        `系统菜单策略 - 菜单树数据: ${JSON.stringify(menuTree.map(m => ({ id: m.id, name: m.name, path: m.path })))}`,
      );

      return menuTree;
    } catch (error) {
      console.error(`系统菜单策略 - 获取系统菜单树失败: ${error.message}`, error);
      this.logger.error(`获取系统菜单树失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取指定ID的系统菜单
   * @param id 菜单ID
   * @returns 菜单信息
   */
  async findOne(id: number): Promise<MenuDto> {
    const menu = await this.prisma.systemMenu.findUnique({
      where: { id },
    });

    if (!menu) {
      throw new NotFoundException(`菜单ID ${id} 不存在`);
    }

    // 转换返回结果
    return this.transformMenuData(menu);
  }

  /**
   * 更新系统菜单
   * @param id 菜单ID
   * @param updateMenuDto 更新菜单DTO
   * @returns 更新后的菜单
   */
  async update(id: number, updateMenuDto: UpdateMenuDto): Promise<MenuDto> {
    const { name, path, meta, ...rest } = updateMenuDto;

    // 检查菜单是否存在
    await this.findOne(id);

    // 检查路径是否已被其他菜单使用
    if (path) {
      const existingPath = await this.prisma.systemMenu.findFirst({
        where: {
          path,
          id: { not: id },
        },
      });

      if (existingPath) {
        throw new ConflictException(`菜单路径 ${path} 已被其他菜单使用`);
      }
    }

    // 更新菜单
    const menu = await this.prisma.systemMenu.update({
      where: { id },
      data: {
        name,
        path,
        meta: meta ? JSON.stringify(meta) : undefined,
        ...rest,
      },
    });

    this.logger.log(`更新系统菜单: ${id}`);

    // 转换返回结果
    return this.transformMenuData(menu);
  }

  /**
   * 删除系统菜单
   * @param id 菜单ID
   * @returns 删除结果
   */
  async remove(id: number): Promise<{ success: boolean }> {
    // 检查菜单是否存在
    await this.findOne(id);

    // 检查是否有子菜单
    const children = await this.prisma.systemMenu.findMany({
      where: { pid: id },
    });

    if (children.length > 0) {
      throw new ConflictException(`菜单ID ${id} 有子菜单，无法删除`);
    }

    // 删除菜单
    await this.prisma.systemMenu.delete({
      where: { id },
    });

    this.logger.log(`删除系统菜单: ${id}`);

    return { success: true };
  }

  /**
   * 检查菜单名称是否存在
   * @param name 菜单名称
   * @param id 排除的菜单ID（可选）
   * @returns 是否存在
   */
  async isNameExists(name: string, tenantId?: string, id?: number): Promise<boolean> {
    const menu = await this.prisma.systemMenu.findFirst({
      where: {
        name,
        ...(id ? { id: { not: id } } : {}),
      },
    });

    return !!menu;
  }

  /**
   * 检查菜单路径是否存在
   * @param path 菜单路径
   * @param id 排除的菜单ID（可选）
   * @returns 是否存在
   */
  async isPathExists(path: string, tenantId?: string, id?: number): Promise<boolean> {
    const menu = await this.prisma.systemMenu.findFirst({
      where: {
        path,
        ...(id ? { id: { not: id } } : {}),
      },
    });

    return !!menu;
  }

  /**
   * 根据ID查询菜单
   * @param id 菜单ID
   * @returns 菜单信息
   */
  async findById(id: number): Promise<MenuDto | null> {
    const menu = await this.prisma.systemMenu.findUnique({
      where: { id },
    });

    if (!menu) {
      return null;
    }

    return this.transformMenuData(menu);
  }

  /**
   * 检查是否有子菜单
   * @param parentId 父菜单ID
   * @returns 是否有子菜单
   */
  async hasChildren(parentId: number): Promise<boolean> {
    const count = await this.prisma.systemMenu.count({
      where: { pid: parentId },
    });

    return count > 0;
  }

  /**
   * 构建菜单树
   * @param menus 菜单列表
   * @param pid 父菜单ID
   * @returns 菜单树
   */
  private buildMenuTree(menus: any[], pid: number = null): MenuDto[] {
    const result: MenuDto[] = [];

    menus.forEach(menu => {
      if (menu.pid === pid) {
        // 转换菜单数据
        const transformedMenu = this.transformMenuData(menu);

        // 递归处理子菜单
        const children = this.buildMenuTree(menus, menu.id);
        if (children.length > 0) {
          transformedMenu.children = children;
        }

        result.push(transformedMenu);
      }
    });

    return result;
  }

  /**
   * 构建前端菜单树
   * @param menus 菜单列表
   * @param pid 父菜单ID
   * @returns 菜单树
   */
  private buildFrontendMenuTree(menus: any[], pid: number = null): MenuTreeDto[] {
    this.logger.log(`构建前端菜单树，父ID: ${pid || '根级'}, 菜单总数: ${menus.length}`);

    const result: MenuTreeDto[] = [];
    const filteredMenus = menus.filter(menu => menu.pid === pid);

    this.logger.log(`找到 ${filteredMenus.length} 个父ID为 ${pid || '根级'} 的菜单`);

    filteredMenus.forEach(menu => {
      try {
        // 转换菜单数据
        this.logger.log(`处理菜单: ID=${menu.id}, 名称=${menu.name}, 路径=${menu.path}`);
        const transformedMenu = this.transformFrontendMenuData(menu);

        // 递归处理子菜单
        const children = this.buildFrontendMenuTree(menus, menu.id);
        if (children.length > 0) {
          this.logger.log(`菜单 ${menu.name} 有 ${children.length} 个子菜单`);
          transformedMenu.children = children;
        } else {
          this.logger.log(`菜单 ${menu.name} 没有子菜单`);
        }

        result.push(transformedMenu);
        this.logger.log(`菜单 ${menu.name} 处理完成`);
      } catch (error) {
        this.logger.error(`处理菜单 ${menu.id} 失败: ${error.message}`);
      }
    });

    this.logger.log(`返回 ${result.length} 个菜单项，父ID: ${pid || '根级'}`);
    return result;
  }

  /**
   * 转换菜单数据
   * @param menu 菜单数据
   * @returns 转换后的菜单数据
   */
  private transformMenuData(menu: any): MenuDto {
    let meta: any = { title: menu.name }; // 默认使用菜单名称作为标题
    try {
      if (menu.meta) {
        const parsedMeta = JSON.parse(menu.meta);
        meta = {
          title: parsedMeta.title || menu.name, // 确保有标题
          icon: parsedMeta.icon,
          orderNo: parsedMeta.orderNo,
          hideMenu: parsedMeta.hideMenu,
          ignoreAuth: parsedMeta.ignoreAuth,
          hideBreadcrumb: parsedMeta.hideBreadcrumb,
          hideChildrenInMenu: parsedMeta.hideChildrenInMenu,
          currentActiveMenu: parsedMeta.currentActiveMenu,
        };
      }
    } catch (error) {
      this.logger.error(`解析菜单元数据失败: ${error.message}`);
    }

    return {
      id: menu.id,
      name: menu.name,
      path: menu.path,
      component: menu.component,
      redirect: menu.redirect,
      pid: menu.pid,
      type: menu.type,
      icon: menu.icon,
      permission: menu.permission,
      orderNo: menu.orderNo,
      status: menu.status,
      meta,
      createdAt: menu.createdAt,
      updatedAt: menu.updatedAt,
    };
  }

  /**
   * 转换前端菜单数据
   * @param menu 菜单数据
   * @returns 转换后的前端菜单数据
   */
  private transformFrontendMenuData(menu: any): MenuTreeDto {
    this.logger.log(`转换菜单数据: ID=${menu.id}, 名称=${menu.name}, 路径=${menu.path}`);

    let meta = {};
    try {
      if (menu.meta) {
        this.logger.log(`解析菜单元数据: ${menu.meta}`);
        meta = JSON.parse(menu.meta);
        this.logger.log(`解析后的元数据: ${JSON.stringify(meta)}`);
      } else {
        this.logger.log(`菜单 ${menu.name} 没有元数据`);
      }
    } catch (error) {
      this.logger.error(`解析菜单 ${menu.name} 元数据失败: ${error.message}`);
    }

    const result = {
      id: menu.id.toString(),
      name: menu.name,
      path: menu.path,
      component: menu.component,
      redirect: menu.redirect,
      meta: meta as any,
    };

    this.logger.log(`转换后的菜单数据: ${JSON.stringify(result)}`);
    return result;
  }
}
