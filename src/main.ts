import { ValidationPipe, BadRequestException } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { NestFactory } from '@nestjs/core';
import { DocumentBuilder, SwaggerModule } from '@nestjs/swagger';
import * as bodyParser from 'body-parser';
import helmet from 'helmet';
import * as metadata from 'package.json';

import { AppModule } from './app.module';
import {
  ApiResponseDto,
  SuccessResponseDto,
  PaginationResponseDto,
  ErrorResponseDto,
  ListResponseDto,
} from './core/common/dto/api-response.dto';

async function bootstrap() {
  const app = await NestFactory.create(AppModule);

  // 将应用实例存储到全局，供其他模块使用
  (global as any).app = app;

  const configService = app.get(ConfigService);

  const apiDescription = [
    '多租户SaaS建站系统API接口文档',
    '',
    '## 统一返回格式',
    '所有API接口都返回统一格式: { code: 0, data: {}, message: "操作成功" }',
    '',
    '## 状态码说明',
    '0-成功, -1-失败, 400-参数错误, 401-未授权, 403-禁止访问, 404-不存在, 500-服务器错误',
    '',
    '## 认证方式',
    '请在请求头添加: Authorization: Bearer <token>',
  ].join('\n');

  const swaggerConfig = new DocumentBuilder()
    .setTitle(metadata.name || 'FlexiHub API')
    .setVersion(metadata.version || '1.0.0')
    .setDescription(metadata.description || apiDescription)
    .addBearerAuth()
    .addServer('/api', 'API服务器')
    .build();

  const document = SwaggerModule.createDocument(app, swaggerConfig, {
    extraModels: [
      ApiResponseDto,
      SuccessResponseDto,
      PaginationResponseDto,
      ErrorResponseDto,
      ListResponseDto,
    ],
  });

  SwaggerModule.setup('api/docs', app, document, {
    swaggerOptions: {
      persistAuthorization: true,
      docExpansion: 'none',
      filter: true,
      showRequestDuration: true,
    },
    customCss: `
      .swagger-ui .topbar { display: none; }
      .swagger-ui .info { margin: 20px 0; }
      .swagger-ui .info .title { color: #3b82f6; }
    `,
    customSiteTitle: 'FlexiHub API Documentation',
  });

  app.use(helmet());

  // 使用 body-parser 中间件解析请求体
  app.use(bodyParser.json());
  app.use(bodyParser.urlencoded({ extended: true }));

  app.enableCors({
    origin: configService.get('CORS_ORIGINS'),
  });

  // 设置全局路由前缀
  app.setGlobalPrefix('api');

  // 全局验证管道
  app.useGlobalPipes(
    new ValidationPipe({
      whitelist: true, // 自动删除非DTO中定义的属性
      transform: true, // 自动转换类型
      forbidNonWhitelisted: false, // 允许非白名单属性
      transformOptions: {
        enableImplicitConversion: true, // 启用隐式类型转换
      },
      // 显示详细的验证错误信息
      exceptionFactory: errors => {
        const messages = errors
          .map(error => {
            const constraints = error.constraints || {};
            return Object.values(constraints).join(', ');
          })
          .join('; ');
        return new BadRequestException(messages || 'Validation failed');
      },
    }),
  );

  // 从配置服务获取端口
  const port = configService.get('PORT');

  await app.listen(port);

  // 只在开发环境打印详细信息
  const nodeEnv = configService.get('NODE_ENV');
  if (nodeEnv === 'development') {
    console.log(`应用程序正在运行 (${nodeEnv} 环境)`);
    console.log(`本地访问: http://localhost:${port}`);
    console.log(`Swagger 文档: http://localhost:${port}/api/docs`);
    console.log(`API文档导出: http://localhost:${port}/api/system/swagger/markdown`);
  }
}
bootstrap();
