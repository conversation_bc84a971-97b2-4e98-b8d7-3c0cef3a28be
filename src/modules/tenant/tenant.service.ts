import { Injectable } from '@nestjs/common';

import { CreateTenantDto, TenantDto, TenantListDto, UpdateTenantDto } from './dto/tenant.dto';

import { BaseService } from '@/core/common/base/base.service';
import { PaginationUtil } from '@/core/common/utils/pagination.util';
import { QueryBuilderUtil } from '@/core/common/utils/query-builder.util';
import { DatabaseFactory } from '@/core/database/database.factory';
import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 租户服务
 * 处理租户相关的业务逻辑
 */
@Injectable()
export class TenantService extends BaseService {
  constructor(private readonly prisma: PublicPrismaService) {
    super(TenantService.name);
  }

  /**
   * 查询所有租户
   * @param query 查询参数
   * @returns 租户列表
   */
  async findAll(query: any): Promise<TenantListDto> {
    try {
      // 使用分页工具创建分页选项
      const options = this.createPaginationOptions(query);
      const { page, pageSize } = options;
      const skip = (page - 1) * pageSize;

      // 使用查询构建工具构建查询条件
      const filter = this.buildQuery(query, ['name', 'code', 'domain']);

      // 查询租户总数
      const total = await this.prisma.tenant.count({
        where: filter,
      });

      // 查询租户列表
      const tenants = await this.prisma.tenant.findMany({
        where: filter,
        skip,
        take: pageSize,
        orderBy: { createdAt: 'desc' },
        include: {
          datasource: true,
        },
      });

      // 格式化返回数据
      const formattedTenants = tenants.map(tenant => ({
        id: tenant.id,
        code: tenant.code,
        name: tenant.name,
        website: tenant.website,
        domain: tenant.domain,
        status: tenant.status,
        metadata: tenant.metadata,
        createTime: this.formatDate(tenant.createdAt),
        updateTime: this.formatDate(tenant.updatedAt),
        datasource: tenant.datasource
          ? {
              id: tenant.datasource.id,
              name: tenant.datasource.name,
              url: tenant.datasource.url,
              metadata: tenant.datasource.metadata,
            }
          : null,
      }));

      return {
        items: formattedTenants,
        total,
        page,
        pageSize,
      };
    } catch (error) {
      this.logError('查询租户列表失败', error);
      throw error;
    }
  }

  /**
   * 查询单个租户
   * @param id 租户ID
   * @returns 租户信息
   */
  async findOne(id: string): Promise<TenantDto> {
    try {
      const tenantId = this.safeParseInt(id);
      if (isNaN(tenantId)) {
        this.notFound('租户', id);
      }

      const tenant = await this.prisma.tenant.findFirst({
        where: {
          id: tenantId,
          status: { in: [0, 1] }, // 包含所有状态
        },
        include: {
          datasource: true,
        },
      });

      if (!tenant) {
        this.notFound('租户', id);
      }

      // 格式化返回数据
      return {
        id: tenant.id,
        code: tenant.code,
        name: tenant.name,
        website: tenant.website,
        domain: tenant.domain,
        status: tenant.status,
        metadata: tenant.metadata,
        createTime: this.formatDate(tenant.createdAt),
        updateTime: this.formatDate(tenant.updatedAt),
        datasource: tenant.datasource
          ? {
              id: tenant.datasource.id,
              name: tenant.datasource.name,
              url: tenant.datasource.url,
              metadata: tenant.datasource.metadata,
            }
          : null,
      };
    } catch (error) {
      this.logError(`查询租户详情失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 创建租户
   * @param createTenantDto 创建租户数据
   * @returns 创建的租户
   */
  async create(createTenantDto: CreateTenantDto): Promise<TenantDto> {
    try {
      // 检查租户代码是否已存在
      const codeExists = await this.checkCodeExists(createTenantDto.code);
      if (codeExists) {
        this.alreadyExists('租户', '代码', createTenantDto.code);
      }

      // 创建数据源
      const datasource = await this.prisma.datasource.create({
        data: {
          name: createTenantDto.datasource.name,
          url: createTenantDto.datasource.url,
          metadata: {},
        },
      });

      // 处理网站字段，如果为空字符串则设置为null，避免URL验证
      const website = createTenantDto.website === '' ? null : createTenantDto.website;

      // 处理域名字段，如果为空字符串则设置为null
      const domain = createTenantDto.domain === '' ? null : createTenantDto.domain;

      // 如果提供了域名，检查域名是否已被使用
      if (domain) {
        const domainExists = await this.prisma.tenant.findFirst({
          where: { domain },
        });

        if (domainExists) {
          this.alreadyExists('租户', '域名', domain);
        }
      }

      // 创建租户
      const tenant = await this.prisma.tenant.create({
        data: {
          code: createTenantDto.code,
          name: createTenantDto.name,
          website,
          domain,
          status: createTenantDto.status || 1,
          metadata: createTenantDto.metadata || {},
          datasourceId: datasource.id,
        },
        include: {
          datasource: true,
        },
      });

      this.logger.log(`创建租户成功: ${tenant.name}，ID: ${tenant.id}`);

      // 格式化返回数据
      return {
        id: tenant.id,
        code: tenant.code,
        name: tenant.name,
        website: tenant.website,
        domain: tenant.domain,
        status: tenant.status,
        metadata: tenant.metadata,
        createTime: this.formatDate(tenant.createdAt),
        updateTime: this.formatDate(tenant.updatedAt),
        datasource: tenant.datasource
          ? {
              id: tenant.datasource.id,
              name: tenant.datasource.name,
              url: tenant.datasource.url,
              metadata: tenant.datasource.metadata,
            }
          : null,
      };
    } catch (error) {
      this.logError('创建租户失败', error);
      throw error;
    }
  }

  /**
   * 更新租户
   * @param id 租户ID
   * @param updateTenantDto 更新租户数据
   * @returns 更新后的租户
   */
  async update(id: string, updateTenantDto: UpdateTenantDto): Promise<TenantDto> {
    try {
      const tenantId = this.safeParseInt(id);
      if (isNaN(tenantId)) {
        this.notFound('租户', id);
      }

      // 检查租户是否存在
      const existingTenant = await this.prisma.tenant.findFirst({
        where: {
          id: tenantId,
          status: { in: [0, 1] }, // 包含所有状态
        },
      });

      if (!existingTenant) {
        this.notFound('租户', id);
      }

      // 更新租户
      const updateData: any = {};

      // 只有当明确提供了名称时才更新名称
      if (updateTenantDto.name !== undefined) {
        updateData.name = updateTenantDto.name;
      }

      // 只有当明确提供了网站时才更新网站
      if (updateTenantDto.website !== undefined) {
        // 如果网站为空字符串，则设置为null，避免URL验证
        updateData.website = updateTenantDto.website === '' ? null : updateTenantDto.website;
      }

      // 只有当明确提供了域名时才更新域名
      if (updateTenantDto.domain !== undefined) {
        const domain = updateTenantDto.domain === '' ? null : updateTenantDto.domain;

        // 如果提供了域名，检查域名是否已被其他租户使用
        if (domain) {
          const domainExists = await this.prisma.tenant.findFirst({
            where: {
              domain,
              id: { not: tenantId }, // 排除当前租户
            },
          });

          if (domainExists) {
            this.alreadyExists('租户', '域名', domain);
          }
        }

        updateData.domain = domain;
      }

      // 只有当明确提供了元数据时才更新元数据
      if (updateTenantDto.metadata !== undefined) {
        updateData.metadata = updateTenantDto.metadata;
      }

      // 只有当明确提供了状态值时才更新状态
      if (updateTenantDto.status !== undefined) {
        updateData.status = updateTenantDto.status;
      }

      const tenant = await this.prisma.tenant.update({
        where: { id: tenantId },
        data: updateData,
        include: {
          datasource: true,
        },
      });

      this.logger.log(`更新租户成功: ${tenant.name}，ID: ${tenant.id}`);

      // 格式化返回数据
      return {
        id: tenant.id,
        code: tenant.code,
        name: tenant.name,
        website: tenant.website,
        domain: tenant.domain,
        status: tenant.status,
        metadata: tenant.metadata,
        createTime: this.formatDate(tenant.createdAt),
        updateTime: this.formatDate(tenant.updatedAt),
        datasource: tenant.datasource
          ? {
              id: tenant.datasource.id,
              name: tenant.datasource.name,
              url: tenant.datasource.url,
              metadata: tenant.datasource.metadata,
            }
          : null,
      };
    } catch (error) {
      this.logError(`更新租户失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 删除租户
   * @param id 租户ID
   * @returns 删除结果
   */
  async remove(id: string): Promise<{ success: boolean }> {
    try {
      const tenantId = this.safeParseInt(id);
      if (isNaN(tenantId)) {
        this.notFound('租户', id);
      }

      // 检查租户是否存在
      const existingTenant = await this.prisma.tenant.findFirst({
        where: {
          id: tenantId,
          status: { in: [0, 1] }, // 包含所有状态
        },
        include: {
          datasource: true,
        },
      });

      if (!existingTenant) {
        this.notFound('租户', id);
      }

      // 删除租户
      await this.prisma.tenant.delete({
        where: { id: tenantId },
      });

      // 删除关联的数据源
      if (existingTenant.datasourceId) {
        await this.prisma.datasource.delete({
          where: { id: existingTenant.datasourceId },
        });
      }

      this.logger.log(`删除租户成功: ${existingTenant.name}，ID: ${existingTenant.id}`);

      return { success: true };
    } catch (error) {
      this.logError(`删除租户失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 更新租户状态
   * @param id 租户ID
   * @param status 状态值
   * @returns 更新结果
   */
  async updateStatus(id: string, status: number): Promise<{ id: string; status: number }> {
    try {
      const tenantId = this.safeParseInt(id);
      if (isNaN(tenantId)) {
        this.notFound('租户', id);
      }

      // 检查租户是否存在
      const existingTenant = await this.prisma.tenant.findFirst({
        where: {
          id: tenantId,
          status: { in: [0, 1] }, // 包含所有状态
        },
      });

      if (!existingTenant) {
        this.notFound('租户', id);
      }

      // 验证状态值
      if (status !== 0 && status !== 1) {
        this.validationError('租户状态值无效，只能为0或1');
      }

      // 更新租户状态
      await this.prisma.tenant.update({
        where: { id: tenantId },
        data: { status },
      });

      this.logger.log(
        `更新租户状态成功: ${existingTenant.name}，ID: ${existingTenant.id}，状态: ${status}`,
      );

      return { id, status };
    } catch (error) {
      this.logError(`更新租户状态失败，ID: ${id}，状态: ${status}`, error);
      throw error;
    }
  }

  /**
   * 检查租户代码是否存在
   * @param code 租户代码
   * @param id 排除的租户ID（可选）
   * @returns 是否存在
   */
  async checkCodeExists(code: string, id?: string): Promise<boolean> {
    try {
      const where: any = {
        code,
        status: { in: [0, 1] }, // 包含所有状态
      };

      // 如果提供了ID，则排除该ID的租户
      if (id) {
        const tenantId = this.safeParseInt(id);
        if (!isNaN(tenantId)) {
          where.id = { not: tenantId };
        }
      }

      const count = await this.prisma.tenant.count({ where });

      return count > 0;
    } catch (error) {
      this.logError(`检查租户代码是否存在失败，代码: ${code}`, error);
      throw error;
    }
  }

  /**
   * 检查租户域名是否存在
   * @param domain 租户域名
   * @param id 排除的租户ID（可选）
   * @returns 是否存在
   */
  async checkDomainExists(domain: string, id?: string): Promise<boolean> {
    try {
      if (!domain) {
        return false;
      }

      const where: any = {
        domain,
        status: { in: [0, 1] }, // 包含所有状态
      };

      // 如果提供了ID，则排除该ID的租户
      if (id) {
        const tenantId = this.safeParseInt(id);
        if (!isNaN(tenantId)) {
          where.id = { not: tenantId };
        }
      }

      const count = await this.prisma.tenant.count({ where });

      return count > 0;
    } catch (error) {
      this.logError(`检查租户域名是否存在失败，域名: ${domain}`, error);
      throw error;
    }
  }
}
