import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsBoolean, IsInt, IsObject, Min } from 'class-validator';

export class CreateWebsiteTemplateDto {
  @ApiProperty({ description: '模板名称' })
  @IsString()
  name: string;

  @ApiProperty({ description: '模板描述', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: '模板分类',
    examples: ['business', 'portfolio', 'blog', 'ecommerce', 'landing-page', 'corporate'],
  })
  @IsString()
  category: string;

  @ApiProperty({
    description: '行业分类',
    required: false,
    examples: ['tech', 'retail', 'healthcare', 'education', 'finance', 'consulting'],
  })
  @IsOptional()
  @IsString()
  industry?: string;

  @ApiProperty({ description: '模板缩略图URL', required: false })
  @IsOptional()
  @IsString()
  thumbnail?: string;

  @ApiProperty({ description: '预览地址', required: false })
  @IsOptional()
  @IsString()
  preview?: string;

  @ApiProperty({
    description: '模板配置',
    type: 'object',
    example: {
      layout: 'modern',
      colorScheme: 'blue',
      header: {
        type: 'fixed',
        showLogo: true,
        showNavigation: true,
      },
      footer: {
        showCopyright: true,
        showSocialLinks: true,
      },
      pages: [
        { name: 'home', title: '首页', isDefault: true },
        { name: 'about', title: '关于我们' },
        { name: 'contact', title: '联系我们' },
      ],
    },
  })
  @IsObject()
  config: Record<string, any>;

  @ApiProperty({
    description: '模板特性标签',
    type: 'object',
    required: false,
    example: {
      responsive: true,
      seoFriendly: true,
      hasEcommerce: false,
      hasBlog: true,
      hasContactForm: true,
      complexity: 'medium',
    },
  })
  @IsOptional()
  @IsObject()
  features?: Record<string, any>;

  @ApiProperty({ description: '是否为付费模板', default: false })
  @IsOptional()
  @IsBoolean()
  isPremium?: boolean = false;

  @ApiProperty({ description: '是否激活', default: true })
  @IsOptional()
  @IsBoolean()
  isActive?: boolean = true;

  @ApiProperty({ description: '排序权重', default: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  sortOrder?: number = 0;

  @ApiProperty({
    description: '元数据',
    type: 'object',
    required: false,
    example: {
      author: 'Template Designer',
      version: '1.0.0',
      license: 'MIT',
      tags: ['modern', 'clean', 'professional'],
    },
  })
  @IsOptional()
  @IsObject()
  metadata?: Record<string, any>;
}
