import { PrismaModule } from '@core/database/prisma/prisma.module';
import { Module } from '@nestjs/common';

import { WebsiteFormController } from './website-form.controller';
import { WebsiteFormService } from './website-form.service';

@Module({
  imports: [PrismaModule],
  controllers: [WebsiteFormController],
  providers: [WebsiteFormService],
  exports: [WebsiteFormService],
})
export class WebsiteFormModule {}
