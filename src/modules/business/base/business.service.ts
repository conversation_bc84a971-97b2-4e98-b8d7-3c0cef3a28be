import { Injectable, Logger } from '@nestjs/common';

import { TenantDatabaseClient } from '@/core/database/clients/tenant.client';
import { DatabaseFactory } from '@/core/database/database.factory';

/**
 * 业务服务基类
 *
 * 为所有业务功能服务提供基础功能：
 * - Tenant Schema数据库访问
 * - 租户上下文管理
 * - 租户隔离查询助手
 */
@Injectable()
export abstract class BaseBusinessService {
  protected readonly logger: Logger;
  protected tenantDb: TenantDatabaseClient;
  protected currentTenantId?: number;

  constructor(
    private readonly databaseFactory: DatabaseFactory,
    serviceName: string,
  ) {
    this.logger = new Logger(serviceName);
  }

  /**
   * 初始化租户上下文
   * 必须在使用其他方法前调用
   */
  async initTenantContext(tenantId?: number) {
    this.currentTenantId = tenantId;
    this.tenantDb = await this.databaseFactory.getTenantClient(tenantId);
    this.logOperation('租户上下文初始化', { tenantId });
  }

  /**
   * 记录操作日志
   */
  protected logOperation(operation: string, data?: any) {
    const logData = {
      tenantId: this.currentTenantId,
      ...data,
    };
    this.logger.log(`${operation}${data ? `: ${JSON.stringify(logData)}` : ''}`);
  }

  /**
   * 记录错误日志
   */
  protected logError(operation: string, error: Error) {
    this.logger.error(
      `[Tenant:${this.currentTenantId}] ${operation} failed: ${error.message}`,
      error.stack,
    );
  }

  /**
   * 确保租户上下文已初始化
   */
  protected ensureTenantContext() {
    if (!this.tenantDb || this.currentTenantId === undefined) {
      throw new Error('租户上下文未初始化，请先调用 initTenantContext()');
    }
  }

  /**
   * 带租户隔离的查询助手
   */
  protected async findManyWithTenant<T>(model: any, args?: any, tenantId?: number): Promise<T[]> {
    this.ensureTenantContext();
    const effectiveTenantId = tenantId ?? this.currentTenantId;

    return this.tenantDb.findManyWithTenant(model, effectiveTenantId, args);
  }

  /**
   * 带租户隔离的单个查询助手
   */
  protected async findUniqueWithTenant<T>(
    model: any,
    args: any,
    tenantId?: number,
  ): Promise<T | null> {
    this.ensureTenantContext();
    const effectiveTenantId = tenantId ?? this.currentTenantId;

    return this.tenantDb.findUniqueWithTenant(model, effectiveTenantId, args);
  }

  /**
   * 创建带租户ID的数据
   */
  protected async createWithTenant<T>(model: any, data: any, tenantId?: number): Promise<T> {
    this.ensureTenantContext();
    const effectiveTenantId = tenantId ?? this.currentTenantId;

    return model.create({
      data: {
        ...data,
        tenantId: effectiveTenantId,
      },
    });
  }

  /**
   * 更新带租户隔离的数据
   */
  protected async updateWithTenant<T>(
    model: any,
    where: any,
    data: any,
    tenantId?: number,
  ): Promise<T> {
    this.ensureTenantContext();
    const effectiveTenantId = tenantId ?? this.currentTenantId;

    return model.update({
      where: {
        ...where,
        tenantId: effectiveTenantId,
      },
      data,
    });
  }

  /**
   * 删除带租户隔离的数据
   */
  protected async deleteWithTenant<T>(model: any, where: any, tenantId?: number): Promise<T> {
    this.ensureTenantContext();
    const effectiveTenantId = tenantId ?? this.currentTenantId;

    return model.delete({
      where: {
        ...where,
        tenantId: effectiveTenantId,
      },
    });
  }
}
