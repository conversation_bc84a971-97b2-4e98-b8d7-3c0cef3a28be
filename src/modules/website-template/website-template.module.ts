import { PrismaModule } from '@core/database/prisma/prisma.module';
import { Module } from '@nestjs/common';

import { WebsiteTemplateController } from './website-template.controller';
import { WebsiteTemplateService } from './website-template.service';

@Module({
  imports: [PrismaModule],
  controllers: [WebsiteTemplateController],
  providers: [WebsiteTemplateService],
  exports: [WebsiteTemplateService],
})
export class WebsiteTemplateModule {}
