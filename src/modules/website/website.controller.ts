import { CurrentTenant } from '@core/decorators/current-tenant.decorator';
import { CurrentUser } from '@core/decorators/current-user.decorator';
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
  HttpCode,
  HttpStatus,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';

import { CreateWebsiteDto } from './dto/create-website.dto';
import { QueryWebsiteDto } from './dto/query-website.dto';
import { UpdateWebsiteDto } from './dto/update-website.dto';
import { WebsiteEntity } from './entities/website.entity';
import { WebsiteService } from './website.service';

@ApiTags('网站管理')
@ApiBearerAuth()
@Controller('websites')
export class WebsiteController {
  constructor(private readonly websiteService: WebsiteService) {}

  @Post()
  @ApiOperation({ summary: '创建网站' })
  @ApiResponse({
    status: 201,
    description: '网站创建成功',
    type: WebsiteEntity,
  })
  @ApiResponse({ status: 409, description: '子域名或域名已存在' })
  @ApiResponse({ status: 400, description: '模板不存在或不可用' })
  async create(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Body() createDto: CreateWebsiteDto,
  ): Promise<WebsiteEntity> {
    return this.websiteService.create(tenantId, userId, createDto);
  }

  @Get()
  @ApiOperation({ summary: '获取网站列表' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/WebsiteEntity' },
        },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
        totalPages: { type: 'number' },
      },
    },
  })
  async findAll(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Query() queryDto: QueryWebsiteDto,
  ) {
    return this.websiteService.findAll(tenantId, userId, queryDto);
  }

  @Get('stats')
  @ApiOperation({ summary: '获取网站统计信息' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        total: { type: 'number' },
        published: { type: 'number' },
        draft: { type: 'number' },
        archived: { type: 'number' },
      },
    },
  })
  async getStats(@CurrentTenant('id') tenantId: number, @CurrentUser('id') userId: number) {
    return this.websiteService.getWebsiteStats(tenantId, userId);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取单个网站详情' })
  @ApiParam({ name: 'id', description: '网站ID' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: WebsiteEntity,
  })
  @ApiResponse({ status: 404, description: '网站不存在' })
  async findOne(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<WebsiteEntity> {
    return this.websiteService.findOne(tenantId, id, userId);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新网站信息' })
  @ApiParam({ name: 'id', description: '网站ID' })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    type: WebsiteEntity,
  })
  @ApiResponse({ status: 404, description: '网站不存在' })
  @ApiResponse({ status: 409, description: '子域名或域名冲突' })
  async update(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateWebsiteDto,
  ): Promise<WebsiteEntity> {
    return this.websiteService.update(tenantId, id, updateDto, userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除网站' })
  @ApiParam({ name: 'id', description: '网站ID' })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiResponse({ status: 404, description: '网站不存在' })
  async remove(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<{ message: string }> {
    await this.websiteService.remove(tenantId, id, userId);
    return { message: '网站删除成功' };
  }

  @Post(':id/publish')
  @ApiOperation({ summary: '发布网站' })
  @ApiParam({ name: 'id', description: '网站ID' })
  @ApiResponse({
    status: 200,
    description: '发布成功',
    type: WebsiteEntity,
  })
  @ApiResponse({ status: 404, description: '网站不存在' })
  @ApiResponse({ status: 400, description: '网站已经是发布状态' })
  async publish(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<WebsiteEntity> {
    return this.websiteService.publish(tenantId, id, userId);
  }

  @Post(':id/unpublish')
  @ApiOperation({ summary: '取消发布网站' })
  @ApiParam({ name: 'id', description: '网站ID' })
  @ApiResponse({
    status: 200,
    description: '取消发布成功',
    type: WebsiteEntity,
  })
  @ApiResponse({ status: 404, description: '网站不存在' })
  @ApiResponse({ status: 400, description: '网站未处于发布状态' })
  async unpublish(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<WebsiteEntity> {
    return this.websiteService.unpublish(tenantId, id, userId);
  }

  @Post('from-template/:templateId')
  @ApiOperation({ summary: '基于模板创建网站' })
  @ApiParam({ name: 'templateId', description: '模板ID' })
  @ApiResponse({
    status: 201,
    description: '创建成功',
    type: WebsiteEntity,
  })
  @ApiResponse({ status: 400, description: '模板不存在或不可用' })
  async createFromTemplate(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Param('templateId', ParseIntPipe) templateId: number,
    @Body() websiteData: { name: string; description?: string; subdomain?: string },
  ): Promise<WebsiteEntity> {
    return this.websiteService.createFromTemplate(tenantId, userId, templateId, websiteData);
  }
}
