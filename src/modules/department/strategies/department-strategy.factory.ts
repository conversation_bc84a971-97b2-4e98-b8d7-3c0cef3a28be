import { Injectable, Logger } from '@nestjs/common';

import { IDepartmentStrategy } from './department-strategy.interface';
import { SystemDepartmentStrategy } from './system-department.strategy';

import { DatabaseFactory } from '@/core/database/database.factory';
/**
 * 部门策略类型枚举
 */
export enum DepartmentStrategyType {
  SYSTEM = 'SYSTEM',
  TENANT = 'TENANT',
}

/**
 * 部门策略工厂
 * 用于注册和获取不同类型的部门策略
 */
@Injectable()
export class DepartmentStrategyFactory {
  private readonly logger = new Logger(DepartmentStrategyFactory.name);
  private readonly strategies = new Map<string, IDepartmentStrategy>();

  /**
   * 注册策略
   * @param strategy 策略实例
   */
  register(strategy: IDepartmentStrategy): void {
    if (!strategy) {
      this.logger.error('尝试注册空策略');
      return;
    }

    try {
      const type = strategy.getType();
      this.logger.log(`注册部门策略: ${type}`);
      this.strategies.set(type, strategy);

      // 验证策略是否已经注册
      const registeredStrategy = this.strategies.get(type);
      if (registeredStrategy) {
        this.logger.log(`部门策略已成功注册: ${type}`);
      } else {
        this.logger.error(`部门策略注册失败: ${type}`);
      }
    } catch (error) {
      this.logger.error(`部门策略注册失败: ${error.message}`, error.stack);
    }
  }

  /**
   * 根据用户类型获取策略
   * @param userType 用户类型
   * @returns 策略实例
   */
  getStrategyByUserType(userType: string): IDepartmentStrategy {
    try {
      // 记录当前注册的策略
      this.logger.log(`当前注册的策略数量: ${this.strategies.size}`);
      this.strategies.forEach((strategy, type) => {
        this.logger.log(`已注册策略: ${type}`);
      });

      // 根据用户类型确定策略类型
      const strategyType =
        userType === 'SYSTEM' ? DepartmentStrategyType.SYSTEM : DepartmentStrategyType.TENANT;
      this.logger.log(`根据用户类型 ${userType} 确定策略类型: ${strategyType}`);

      return this.getStrategy(strategyType);
    } catch (error) {
      this.logger.error(`获取策略失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取策略
   * @param type 策略类型
   * @returns 策略实例
   */
  getStrategy(type: string): IDepartmentStrategy {
    this.logger.log(`尝试获取策略: ${type}`);

    // 如果策略类型是枚举值，转换为字符串
    const typeStr = typeof type === 'number' ? DepartmentStrategyType[type] : type;

    const strategy = this.strategies.get(typeStr);
    if (!strategy) {
      this.logger.error(`未找到部门策略: ${typeStr}`);

      // 尝试使用默认策略
      if (typeStr === DepartmentStrategyType.SYSTEM) {
        this.logger.log('尝试使用系统部门策略');
        // 创建一个新的系统部门策略实例
        const systemStrategy = new SystemDepartmentStrategy(new PublicPrismaService());
        this.register(systemStrategy);
        return systemStrategy;
      } else if (typeStr === DepartmentStrategyType.TENANT) {
        this.logger.log('尝试使用租户部门策略');
        // 创建一个新的租户部门策略实例
        // 注意：这里不能创建租户部门策略，因为它需要请求作用域的 TENANT_PRISMA_SERVICE
      }

      throw new Error(`未找到部门策略: ${typeStr}`);
    }

    this.logger.log(`成功获取策略: ${typeStr}`);
    return strategy;
  }
}
