import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsString, IsBoolean, IsInt, IsDate } from 'class-validator';

export class SitemapEntity {
  @ApiProperty({ description: '站点地图ID' })
  id: number;

  @ApiProperty({ description: '所属网站ID' })
  websiteId: number;

  @ApiProperty({ description: '站点地图类型', enum: ['xml', 'html', 'txt'], default: 'xml' })
  @IsString()
  type: string;

  @ApiProperty({ description: '站点地图URL' })
  @IsString()
  url: string;

  @ApiProperty({ description: '站点地图内容' })
  @IsString()
  content: string;

  @ApiProperty({ description: '最后生成时间' })
  @IsDate()
  @Type(() => Date)
  lastGenerated: Date;

  @ApiProperty({ description: '是否激活', default: true })
  @IsBoolean()
  isActive: boolean;

  @ApiProperty({ description: '租户ID' })
  tenantId: number;

  @ApiProperty({ description: '创建时间' })
  @IsDate()
  @Type(() => Date)
  createTime: Date;

  @ApiProperty({ description: '更新时间' })
  @IsDate()
  @Type(() => Date)
  updateTime: Date;

  // 关联对象（可选）
  @ApiProperty({ description: '网站信息', required: false })
  website?: any;
}
