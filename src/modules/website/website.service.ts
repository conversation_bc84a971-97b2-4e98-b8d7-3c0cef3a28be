import { Injectable } from '@nestjs/common';

import { DatabaseFactory } from '@/core/database/database.factory';
import { BaseBusinessService } from '@/modules/business/base/business.service';

@Injectable()
export class WebsiteService extends BaseBusinessService {
  constructor(databaseFactory: DatabaseFactory) {
    super(databaseFactory, 'WebsiteService');
  }

  // TODO: 实现网站功能
  async getWebsites() {
    throw new Error('Method not implemented - Schema needs to be verified');
  }
}
