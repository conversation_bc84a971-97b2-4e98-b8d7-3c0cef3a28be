import { Injectable } from '@nestjs/common';

import { DataMapperUtil } from '@/core/common/utils/data-mapper.util';
import { BasePlatformService } from '@/modules/platform/base/platform.service';

/**
 * 租户管理服务
 *
 * 提供租户的查询、统计等功能
 */
@Injectable()
export class TenantManagementService extends BasePlatformService {
  // 使用统一的数据映射
  private mapToEntity(dbTenant: any): any {
    const baseFields = DataMapperUtil.mapBaseEntity(dbTenant);

    return {
      ...baseFields,
      name: dbTenant.name,
      code: dbTenant.code,
      domain: dbTenant.domain,
      email: dbTenant.email,
      phone: dbTenant.phone,
      status: DataMapperUtil.mapEnumField(dbTenant.status, ['0', '1'] as const, '1'),
      deploymentMode: dbTenant.deploymentMode || 'shared',
      sortOrder: dbTenant.sortOrder || 0,
      metadata: DataMapperUtil.mapJsonField(dbTenant.metadata),
      // 处理关联数据
      datasourceConfig: dbTenant.datasource
        ? {
            id: dbTenant.datasource.id,
            name: dbTenant.datasource.name,
            url: dbTenant.datasource.url,
            type: dbTenant.datasource.type,
            status: dbTenant.datasource.status,
            metadata: DataMapperUtil.mapJsonField(dbTenant.datasource.metadata),
          }
        : null,
      subscriptionInfo: dbTenant.activeSubscription
        ? {
            planName: DataMapperUtil.safeGet(dbTenant, 'activeSubscription.plan.name'),
            status: dbTenant.activeSubscription.status,
            startDate: dbTenant.activeSubscription.startDate,
            endDate: dbTenant.activeSubscription.endDate,
          }
        : null,
    };
  }

  // 更新列表查询方法
  async findMany(query: any): Promise<any> {
    const { page = 1, limit = 10, search, status } = query;
    const skip = (page - 1) * limit;

    const where: any = {};

    if (search) {
      where.OR = [
        { name: { contains: search, mode: 'insensitive' } },
        { code: { contains: search, mode: 'insensitive' } },
        { email: { contains: search, mode: 'insensitive' } },
      ];
    }

    if (status !== undefined) {
      where.status = parseInt(status);
    }

    const [tenants, total] = await Promise.all([
      this.publicDb.tenant.findMany({
        where,
        skip,
        take: limit,
        orderBy: { createTime: 'desc' },
        include: {
          datasource: true,
          subscriptions: {
            where: { status: 'active' },
            include: { plan: true },
            take: 1,
            orderBy: { endDate: 'desc' },
          },
        },
      }),
      this.publicDb.tenant.count({ where }),
    ]);

    // 处理关联数据
    const processedTenants = tenants.map(tenant => ({
      ...tenant,
      activeSubscription: tenant.subscriptions[0] || null,
    }));

    // 使用统一的分页响应构建
    const items = processedTenants.map(tenant => this.mapToEntity(tenant));

    return DataMapperUtil.buildPagedResponse(items, total, page, limit);
  }
}
