import { Injectable, Inject } from '@nestjs/common';

import { CreateMembershipPlanDto, UpdateMembershipPlanDto } from '../dto/membership-plan.dto';

import { BaseService } from '@/core/common/base/base.service';
import {
  TenantPrismaService,
  TENANT_PRISMA_SERVICE,
} from '@/core/database/prisma/tenant-prisma.service';
import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 租户会员计划策略
 * 管理租户数据库中的会员计划
 */
@Injectable()
export class TenantMembershipPlanStrategy extends BaseService {
  constructor(@Inject(TENANT_PRISMA_SERVICE) private readonly tenantPrisma: TenantPrismaService) {
    super(TenantMembershipPlanStrategy.name);
  }

  /**
   * 创建会员计划
   * @param createDto 创建数据
   * @param tenantId 租户ID
   * @returns 创建的会员计划
   */
  async create(createDto: CreateMembershipPlanDto, tenantId: string) {
    try {
      const now = new Date();

      // 创建会员计划记录
      const plan = await (this.tenantPrisma as any).membershipPlan.create({
        data: {
          code: createDto.code,
          name: createDto.name,
          description: createDto.description,
          price: createDto.price,
          originalPrice: createDto.originalPrice,
          billingCycle: createDto.billingCycle,
          features: createDto.features,
          isActive: createDto.isActive ?? true,
          status: 'active',
          sortOrder: createDto.sortOrder ?? 0,
          tenantId,
          createTime: now,
          updateTime: now,
        },
      });

      return plan;
    } catch (error) {
      this.logError('创建会员计划失败', error);
      throw error;
    }
  }

  /**
   * 查询会员计划列表
   * @param where 查询条件
   * @param options 分页选项
   * @param tenantId 租户ID
   * @returns 会员计划列表
   */
  async findAll(where: any, options: PaginationOptions, tenantId: string) {
    try {
      // 添加租户过滤条件
      const filter = {
        ...where,
        tenantId,
      };

      // 查询数据
      const [items, total] = await Promise.all([
        (this.tenantPrisma as any).membershipPlan.findMany({
          where: filter,
          orderBy: [{ sortOrder: 'asc' }, { createTime: 'desc' }],
          skip: options.skip,
          take: options.take,
        }),
        (this.tenantPrisma as any).membershipPlan.count({
          where: filter,
        }),
      ]);

      return {
        items,
        total,
        page: options.page,
        pageSize: options.pageSize,
        totalPages: Math.ceil(total / options.pageSize),
      };
    } catch (error) {
      this.logError('查询会员计划列表失败', error);
      throw error;
    }
  }

  /**
   * 查询单个会员计划
   * @param id 会员计划ID
   * @param tenantId 租户ID
   * @returns 会员计划信息
   */
  async findOne(id: number, tenantId: string) {
    try {
      return (this.tenantPrisma as any).membershipPlan.findFirst({
        where: {
          id,
          tenantId,
        },
      });
    } catch (error) {
      this.logError(`查询会员计划失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 更新会员计划
   * @param id 会员计划ID
   * @param updateDto 更新数据
   * @param tenantId 租户ID
   * @returns 更新后的会员计划
   */
  async update(id: number, updateDto: UpdateMembershipPlanDto, tenantId: string) {
    try {
      return (this.tenantPrisma as any).membershipPlan.update({
        where: {
          id,
          tenantId,
        },
        data: {
          ...updateDto,
          updateTime: new Date(),
        },
      });
    } catch (error) {
      this.logError(`更新会员计划失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 删除会员计划
   * @param id 会员计划ID
   * @param tenantId 租户ID
   * @returns 删除结果
   */
  async remove(id: number, tenantId: string) {
    try {
      // 查询是否有用户关联了此会员计划
      const usageCount = await (this.tenantPrisma as any).userMembership.count({
        where: {
          planId: id,
          tenantId,
        },
      });

      // 如果有用户使用此计划，则标记为非活跃而不是删除
      if (usageCount > 0) {
        return (this.tenantPrisma as any).membershipPlan.update({
          where: {
            id,
            tenantId,
          },
          data: {
            isActive: false,
            status: 'inactive',
            updateTime: new Date(),
          },
        });
      }

      // 如果没有用户使用，可以安全删除
      return (this.tenantPrisma as any).membershipPlan.delete({
        where: {
          id,
          tenantId,
        },
      });
    } catch (error) {
      this.logError(`删除会员计划失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 检查计划代码是否存在
   * @param code 计划代码
   * @param tenantId 租户ID
   * @returns 是否存在
   */
  async isCodeExists(code: string, tenantId: string): Promise<boolean> {
    try {
      const count = await (this.tenantPrisma as any).membershipPlan.count({
        where: {
          code,
          tenantId,
        },
      });
      return count > 0;
    } catch (error) {
      this.logError(`检查计划代码是否存在失败，代码: ${code}`, error);
      return false;
    }
  }
}
