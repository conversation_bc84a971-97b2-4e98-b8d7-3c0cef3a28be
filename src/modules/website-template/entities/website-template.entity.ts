import { ApiProperty } from '@nestjs/swagger';
import { JsonValue } from '@prisma/client/runtime/library';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsBoolean, IsInt, IsObject, IsArray, Min } from 'class-validator';

export class WebsiteTemplateEntity {
  @ApiProperty({ description: '模板ID' })
  id: number;

  @ApiProperty({ description: '模板名称' })
  @IsString()
  name: string;

  @ApiProperty({ description: '模板描述', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: '模板分类',
    examples: ['business', 'portfolio', 'blog', 'ecommerce'],
  })
  @IsString()
  category: string;

  @ApiProperty({
    description: '行业分类',
    required: false,
    examples: ['tech', 'retail', 'healthcare'],
  })
  @IsOptional()
  @IsString()
  industry?: string;

  @ApiProperty({ description: '模板缩略图URL', required: false })
  @IsOptional()
  @IsString()
  thumbnail?: string;

  @ApiProperty({ description: '预览地址', required: false })
  @IsOptional()
  @IsString()
  preview?: string;

  @ApiProperty({ description: '模板配置', type: 'object' })
  @IsObject()
  config: JsonValue;

  @ApiProperty({ description: '模板特性标签', type: 'object', required: false })
  @IsOptional()
  @IsObject()
  features?: JsonValue;

  @ApiProperty({ description: '是否为付费模板', default: false })
  @IsBoolean()
  isPremium: boolean;

  @ApiProperty({ description: '是否激活', default: true })
  @IsBoolean()
  isActive: boolean;

  @ApiProperty({ description: '排序权重', default: 0 })
  @IsInt()
  @Min(0)
  sortOrder: number;

  @ApiProperty({ description: '元数据', type: 'object', required: false })
  @IsOptional()
  @IsObject()
  metadata?: JsonValue;

  @ApiProperty({ description: '租户ID' })
  tenantId: number;

  @ApiProperty({ description: '创建时间' })
  createTime: Date;

  @ApiProperty({ description: '更新时间' })
  updateTime: Date;
}
