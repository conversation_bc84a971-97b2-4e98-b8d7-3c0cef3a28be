import { Injectable, NotFoundException, BadRequestException } from '@nestjs/common';
import type { User } from '@prisma/client';

import { BaseBusinessService } from '../base/business.service';

import { DatabaseFactory } from '@/core/database/database.factory';

/**
 * 业务用户管理服务
 *
 * 负责租户内的用户管理功能：
 * - 用户创建、更新、删除
 * - 用户查询和分页
 * - 租户隔离保障
 */
@Injectable()
export class UserManagementService extends BaseBusinessService {
  constructor(databaseFactory: DatabaseFactory) {
    super(databaseFactory, UserManagementService.name);
  }

  /**
   * 获取租户内的用户列表
   */
  async findAllByTenant(
    tenantId: number,
    options?: {
      page?: number;
      limit?: number;
      search?: string;
      userType?: string;
    },
  ) {
    await this.initTenantContext(tenantId);

    this.logOperation('获取用户列表', options);

    try {
      const { page = 1, limit = 10, search, userType } = options || {};
      const skip = (page - 1) * limit;

      // 构建查询条件
      const where: any = { tenantId };

      if (search) {
        where.OR = [
          { username: { contains: search, mode: 'insensitive' } },
          { email: { contains: search, mode: 'insensitive' } },
          { realName: { contains: search, mode: 'insensitive' } },
        ];
      }

      if (userType) {
        where.userType = userType;
      }

      // 查询用户和总数
      const [users, total] = await Promise.all([
        this.tenantDb.user.findMany({
          where,
          skip,
          take: limit,
          select: {
            id: true,
            username: true,
            email: true,
            realName: true,
            phoneNumber: true,
            avatar: true,
            status: true,
            userType: true,
            createTime: true,
            updateTime: true,
          },
          orderBy: { createTime: 'desc' },
        }),
        this.tenantDb.user.count({ where }),
      ]);

      return {
        data: users,
        pagination: {
          page,
          limit,
          total,
          pages: Math.ceil(total / limit),
        },
      };
    } catch (error) {
      this.logError('获取用户列表', error as Error);
      throw error;
    }
  }

  /**
   * 根据ID获取用户详情
   */
  async findOne(id: number, tenantId: number) {
    await this.initTenantContext(tenantId);

    this.logOperation('获取用户详情', { id });

    try {
      const user = await this.tenantDb.user.findFirst({
        where: {
          id,
          tenantId,
        },
        select: {
          id: true,
          username: true,
          email: true,
          realName: true,
          phoneNumber: true,
          avatar: true,
          status: true,
          userType: true,
          metadata: true,
          createTime: true,
          updateTime: true,
        },
      });

      if (!user) {
        throw new NotFoundException(`用户 ${id} 不存在`);
      }

      return user;
    } catch (error) {
      this.logError('获取用户详情', error as Error);
      throw error;
    }
  }

  /**
   * 创建新用户
   */
  async create(
    tenantId: number,
    createData: {
      username: string;
      password: string;
      email?: string;
      realName?: string;
      phoneNumber?: string;
      userType?: string;
    },
  ) {
    await this.initTenantContext(tenantId);

    this.logOperation('创建用户', { username: createData.username });

    try {
      // 检查用户名是否已存在（租户内唯一）
      const existingUser = await this.tenantDb.user.findFirst({
        where: {
          username: createData.username,
          tenantId,
        },
      });

      if (existingUser) {
        throw new BadRequestException(`用户名 ${createData.username} 已存在`);
      }

      // 检查邮箱是否已被使用（租户内唯一）
      if (createData.email) {
        const existingEmail = await this.tenantDb.user.findFirst({
          where: {
            email: createData.email,
            tenantId,
          },
        });

        if (existingEmail) {
          throw new BadRequestException(`邮箱 ${createData.email} 已被使用`);
        }
      }

      // 创建用户
      const user = await this.createWithTenant(
        this.tenantDb.user,
        {
          username: createData.username,
          password: createData.password, // 注意：实际应用中应该先加密
          email: createData.email,
          realName: createData.realName,
          phoneNumber: createData.phoneNumber,
          userType: createData.userType || 'TENANT',
          status: 1,
        },
        tenantId,
      );

      this.logOperation('用户创建成功', {
        userId: user.id,
        username: user.username,
      });

      // 返回时排除密码
      const { password, ...userWithoutPassword } = user;
      return userWithoutPassword;
    } catch (error) {
      this.logError('创建用户', error as Error);
      throw error;
    }
  }

  /**
   * 更新用户信息
   */
  async update(
    id: number,
    tenantId: number,
    updateData: {
      email?: string;
      realName?: string;
      phoneNumber?: string;
      avatar?: string;
      status?: number;
    },
  ) {
    await this.initTenantContext(tenantId);

    this.logOperation('更新用户', { id, ...updateData });

    try {
      // 验证用户存在
      await this.findOne(id, tenantId);

      // 检查邮箱冲突
      if (updateData.email) {
        const emailConflict = await this.tenantDb.user.findFirst({
          where: {
            email: updateData.email,
            tenantId,
            id: { not: id },
          },
        });

        if (emailConflict) {
          throw new BadRequestException(`邮箱 ${updateData.email} 已被使用`);
        }
      }

      const updatedUser = await this.updateWithTenant(
        this.tenantDb.user,
        { id },
        updateData,
        tenantId,
      );

      this.logOperation('用户更新成功', { id });

      // 返回时排除密码
      const { password, ...userWithoutPassword } = updatedUser;
      return userWithoutPassword;
    } catch (error) {
      this.logError('更新用户', error as Error);
      throw error;
    }
  }

  /**
   * 删除用户
   */
  async remove(id: number, tenantId: number) {
    await this.initTenantContext(tenantId);

    this.logOperation('删除用户', { id });

    try {
      // 验证用户存在
      await this.findOne(id, tenantId);

      const deletedUser = await this.deleteWithTenant(this.tenantDb.user, { id }, tenantId);

      this.logOperation('用户删除成功', { id });

      return { success: true, deletedUserId: deletedUser.id };
    } catch (error) {
      this.logError('删除用户', error as Error);
      throw error;
    }
  }

  /**
   * 更新用户密码
   */
  async updatePassword(
    id: number,
    tenantId: number,
    data: {
      oldPassword: string;
      newPassword: string;
    },
  ) {
    await this.initTenantContext(tenantId);

    this.logOperation('更新用户密码', { id });

    try {
      const user = await this.tenantDb.user.findFirst({
        where: { id, tenantId },
      });

      if (!user) {
        throw new NotFoundException(`用户 ${id} 不存在`);
      }

      // 验证旧密码（这里应该使用加密验证）
      if (user.password !== data.oldPassword) {
        throw new BadRequestException('原密码不正确');
      }

      await this.updateWithTenant(
        this.tenantDb.user,
        { id },
        { password: data.newPassword }, // 实际应用中应该先加密
        tenantId,
      );

      this.logOperation('密码更新成功', { id });

      return { success: true };
    } catch (error) {
      this.logError('更新用户密码', error as Error);
      throw error;
    }
  }

  /**
   * 获取租户用户统计
   */
  async getStatistics(tenantId: number) {
    await this.initTenantContext(tenantId);

    this.logOperation('获取用户统计');

    try {
      const stats = await this.tenantDb.$client.$transaction(async tx => {
        const total = await tx.user.count({ where: { tenantId } });
        const active = await tx.user.count({
          where: { tenantId, status: 1 },
        });
        const byType = await tx.user.groupBy({
          by: ['userType'],
          where: { tenantId },
          _count: true,
        });

        return {
          total,
          active,
          inactive: total - active,
          byType: byType.reduce(
            (acc, item) => {
              acc[item.userType] = item._count;
              return acc;
            },
            {} as Record<string, number>,
          ),
        };
      });

      return stats;
    } catch (error) {
      this.logError('获取用户统计', error as Error);
      throw error;
    }
  }
}
