import * as process from 'process';

import { Injectable, NestMiddleware, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { JwtService } from '@nestjs/jwt';
import { Response } from 'express';

import { isSystemPath } from '@/core/common/constants/system-paths.constant';
import { IRequestWithProps } from '@/core/common/interfaces/request-with-props.interface';
import { DatabaseFactory } from '@/core/database/database.factory';
@Injectable()
export class TenantDatasourceMiddleware implements NestMiddleware {
  private readonly logger = new Logger(TenantDatasourceMiddleware.name);

  constructor(
    private readonly publicPrisma: PublicPrismaService,
    private readonly jwtService: JwtService,
    private readonly configService: ConfigService,
  ) {
    this.logger.log('TenantDatasourceMiddleware initialized');
  }

  /**
   * 检查当前是否为开发环境
   * @returns 是否为开发环境
   */
  private isDevelopmentMode(): boolean {
    const nodeEnv = this.configService.get<string>('NODE_ENV', 'development');
    return nodeEnv === 'development' || nodeEnv === 'dev' || nodeEnv === 'local';
  }

  /**
   * 检查字符串是否为IP地址
   * @param str 要检查的字符串
   * @returns 是否为IP地址
   */
  private isIpAddress(str: string): boolean {
    // IPv4地址正则表达式
    const ipv4Regex = /^(\d{1,3})\.(\d{1,3})\.(\d{1,3})\.(\d{1,3})$/;

    if (!ipv4Regex.test(str)) {
      return false;
    }

    // 检查每个数字是否在0-255范围内
    const parts = str.split('.');
    for (const part of parts) {
      const num = parseInt(part, 10);
      if (num < 0 || num > 255) {
        return false;
      }
    }

    return true;
  }

  /**
   * 从请求中提取域名信息
   * 尝试从多个请求头中获取域名
   * @param request 请求对象
   * @returns 域名信息
   */
  private extractDomainInfo(
    request: IRequestWithProps,
  ): { host: string; domainWithoutPort: string } | null {
    // 尝试从Host头获取
    let host = request.headers.host as string;
    this.logger.debug(`host: ${host}; origin: ${request.headers.origin}`);
    // 如果没有Host头，尝试从Origin头获取
    if (!host && request.headers.origin) {
      try {
        const originUrl = new URL(request.headers.origin as string);
        host = originUrl.host; // 包含端口号
      } catch (e) {
        this.logger.error(`解析Origin头失败: ${e.message}`);
      }
    }

    // 如果仍然没有，尝试从Referer头获取
    if (!host && request.headers.referer) {
      try {
        const refererUrl = new URL(request.headers.referer as string);
        host = refererUrl.host; // 包含端口号
      } catch (e) {
        this.logger.error(`解析Referer头失败: ${e.message}`);
      }
    }

    // 如果没有找到任何域名信息，返回null
    if (!host) {
      return null;
    }

    // 提取不带端口的域名
    const domainWithoutPort = host.split(':')[0];

    return { host, domainWithoutPort };
  }

  async use(request: IRequestWithProps, response: Response, next: () => void) {
    // 记录请求路径
    this.logger.debug(`处理请求: ${request.method} ${request.path}`);

    // 检查是否是系统路径
    const url = request.url || '';
    const pathIsSystem = isSystemPath(url);

    // 尝试从JWT令牌中获取用户类型
    let userType = 'UNKNOWN';
    try {
      const authHeader = request.headers.authorization;
      if (authHeader && authHeader.startsWith('Bearer ')) {
        const token = authHeader.substring(7);
        const decoded = this.jwtService.decode(token);

        if (decoded) {
          userType = decoded['userType'] || 'UNKNOWN';

          // 将用户信息添加到请求中
          request.user = {
            userId: decoded['sub'],
            username: decoded['username'],
            userType: decoded['userType'],
            tenantId: decoded['tenantId'],
          };
        }
      }
    } catch (error) {
      this.logger.warn('解析JWT令牌失败', error);
    }

    // 如果是系统路径，或者用户类型是SYSTEM，则不需要租户信息
    if (pathIsSystem || userType === 'SYSTEM') {
      this.logger.debug('系统用户访问系统路径，跳过租户数据库');
      next();
      return;
    }

    // 1. 首先尝试从域名中识别租户
    let tenantCode: string | undefined;

    // 提取域名信息
    const domainInfo = this.extractDomainInfo(request);

    // 打印域名信息
    if (domainInfo) {
      this.logger.debug(
        `提取到的域名信息: 完整域名=${domainInfo.host}, 不带端口的域名=${domainInfo.domainWithoutPort}`,
      );
    } else {
      this.logger.debug('未能从请求中提取到域名信息');
    }

    // 如果提取到了域名信息，尝试匹配租户
    if (domainInfo) {
      try {
        const { host, domainWithoutPort } = domainInfo;

        // 检查域名是否为IP地址
        const isIp = this.isIpAddress(domainWithoutPort);
        const isDev = this.isDevelopmentMode();

        // 在开发模式下，允许匹配IP地址和localhost
        // 在生产模式下，如果是IP地址，则跳过域名匹配
        if (isIp && !isDev && domainWithoutPort !== '127.0.0.1') {
          this.logger.debug(`域名 ${domainWithoutPort} 是IP地址，当前为生产模式，跳过域名匹配`);
        } else {
          // 如果是开发模式，记录日志
          if (isDev) {
            if (isIp) {
              this.logger.debug(`域名 ${domainWithoutPort} 是IP地址，当前为开发模式，允许匹配`);
            }
            if (domainWithoutPort === 'localhost' || domainWithoutPort === '127.0.0.1') {
              this.logger.debug(`域名是 ${domainWithoutPort}，当前为开发模式，允许匹配`);
            }
          }
          // 先尝试完整匹配（带端口号）
          let tenantByDomain = await this.publicPrisma.tenant.findFirst({
            include: { datasource: true },
            where: { domain: host },
          });

          // 如果没找到，尝试匹配不带端口的域名
          if (!tenantByDomain && domainWithoutPort !== host) {
            this.logger.debug(`完整域名未匹配到租户，尝试匹配不带端口的域名: ${domainWithoutPort}`);
            tenantByDomain = await this.publicPrisma.tenant.findFirst({
              include: { datasource: true },
              where: { domain: domainWithoutPort },
            });
          }

          if (tenantByDomain) {
            tenantCode = tenantByDomain.code;
            this.logger.debug(`通过域名 ${host} 识别到租户: ${tenantCode}`);

            // 检查数据源URL是否存在
            if (!tenantByDomain?.datasource?.url) {
              this.logger.warn(`租户 ${tenantCode} 的数据源URL不存在`);
              // 使用默认数据源URL
              const defaultDatasourceUrl = process.env.DATABASE_URL;
              this.logger.debug(`使用默认数据源URL: ${defaultDatasourceUrl}`);

              request.tenant = {
                tenantId: tenantByDomain.id, // 存储租户的数字ID
                tenantCode: tenantCode,
                datasourceUrl: defaultDatasourceUrl,
              };
            } else {
              this.logger.debug(`租户 ${tenantCode} 的数据源URL: ${tenantByDomain.datasource.url}`);
              request.tenant = {
                tenantId: tenantByDomain.id, // 存储租户的数字ID
                tenantCode: tenantCode,
                datasourceUrl: tenantByDomain.datasource.url,
              };
            }

            // 如果通过域名找到了租户，直接进入下一步
            next();
            return;
          } else {
            this.logger.debug(`域名 ${host} 未匹配到租户，尝试其他方式`);
          }
        }
      } catch (error) {
        this.logger.error(`通过域名查询租户失败: ${error.message}`);
      }
    }

    // 2. 如果域名未匹配到租户，尝试从请求头中获取租户代码
    tenantCode = request.headers['x-tenant-code'] as string;

    // 3. 如果请求头中没有租户代码，尝试从用户信息中获取
    if (!tenantCode && request.user?.tenantId) {
      tenantCode = request.user.tenantId;
      this.logger.debug(`从用户信息中提取租户代码: ${tenantCode}`);
    }

    // 4. 如果有租户代码，则查询租户信息
    if (tenantCode) {
      try {
        const tenant = await this.publicPrisma.tenant.findFirst({
          include: { datasource: true },
          where: { code: tenantCode },
        });

        if (tenant) {
          // 检查数据源URL是否存在
          if (!tenant?.datasource?.url) {
            this.logger.warn(`租户 ${tenantCode} 的数据源URL不存在`);
            // 使用默认数据源URL
            const defaultDatasourceUrl = process.env.DATABASE_URL;
            this.logger.debug(`使用默认数据源URL: ${defaultDatasourceUrl}`);

            request.tenant = {
              tenantId: tenant.id, // 存储租户的数字ID
              tenantCode: tenantCode,
              datasourceUrl: defaultDatasourceUrl,
            };
          } else {
            this.logger.debug(`租户 ${tenantCode} 的数据源URL: ${tenant.datasource.url}`);
            request.tenant = {
              tenantId: tenant.id, // 存储租户的数字ID
              tenantCode: tenantCode,
              datasourceUrl: tenant.datasource.url,
            };
          }
          this.logger.debug(`找到租户: ${tenantCode}, 数据源URL: ${tenant?.datasource?.url}`);
        } else {
          this.logger.warn(`未找到租户: ${tenantCode}`);
        }
      } catch (error) {
        this.logger.error(`查询租户信息失败: ${error.message}`);
      }
    }

    next();
  }
}
