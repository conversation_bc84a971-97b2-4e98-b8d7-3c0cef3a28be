import * as crypto from 'crypto';

import { Injectable, Logger, NotFoundException, ConflictException, Scope } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

import { RoleStrategyType } from './role-strategy.factory';
import { IRoleStrategy } from './role-strategy.interface';
import { CreateRoleDto, RoleDto, RoleListDto, UpdateRoleDto } from '../dto/role.dto';

import { ApiCode, ApiMessage } from '@/core/common/constants/api-code.constant';
import { DateFormatUtil } from '@/core/common/utils/date-format.util';
import { DatabaseFactory } from '@/core/database/database.factory';
import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 系统角色策略
 * 实现系统角色相关的操作
 */
@Injectable({ scope: Scope.DEFAULT })
export class SystemRoleStrategy implements IRoleStrategy {
  private readonly logger = new Logger(SystemRoleStrategy.name);

  constructor(
    private readonly prisma: PublicPrismaService,
    private readonly configService: ConfigService,
  ) {}

  /**
   * 获取默认角色编码
   * @returns 默认角色编码字符串
   */
  private getDefaultRoleCode(): string {
    // 从环境变量获取默认角色编码
    return this.configService.get<string>('DEFAULT_ROLE_CODE', 'USER_100001');
  }

  /**
   * 获取策略类型
   * @returns 策略类型
   */
  getType(): string {
    return RoleStrategyType.SYSTEM;
  }

  /**
   * 创建系统角色
   * @param createRoleDto 创建角色数据
   * @returns 创建的系统角色
   */
  async create(createRoleDto: CreateRoleDto): Promise<RoleDto> {
    try {
      // 如果未提供角色编码，则使用默认编码
      if (!createRoleDto.code) {
        // 使用固定的默认编码
        createRoleDto.code = this.getDefaultRoleCode();
      }

      // 不再检查角色编码唯一性

      // 创建角色
      const role = await this.prisma.systemRole.create({
        data: {
          name: createRoleDto.name,
          code: createRoleDto.code,
          remark: createRoleDto.remark,
          status: createRoleDto.status || 1,
        },
      });

      // 如果提供了权限ID列表，则创建角色权限关联
      if (createRoleDto.permissionIds && createRoleDto.permissionIds.length > 0) {
        const rolePermissions = createRoleDto.permissionIds.map(permissionId => ({
          roleId: role.id,
          permissionId,
        }));

        await this.prisma.systemRolePermission.createMany({
          data: rolePermissions,
        });
      }

      // 如果提供了菜单ID列表，则创建角色菜单关联
      if (createRoleDto.menuIds && createRoleDto.menuIds.length > 0) {
        const roleMenus = createRoleDto.menuIds.map(menuId => ({
          roleId: role.id,
          menuId,
        }));

        await this.prisma.systemRoleMenu.createMany({
          data: roleMenus,
        });
      }

      this.logger.log(`创建系统角色成功: ${role.name}`);

      // 格式化返回数据
      return {
        id: role.id,
        name: role.name,
        code: role.code,
        remark: role.remark,
        status: role.status,
        createTime: DateFormatUtil.formatToDateTime(role.createdAt),
        updateTime: DateFormatUtil.formatToDateTime(role.updatedAt),
        permissions: [],
        menus: [],
      };
    } catch (error) {
      this.logger.error(`创建角色失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 查询所有系统角色
   * @param where 查询条件
   * @param options 分页选项
   * @returns 角色列表
   */
  async findAll(where: any, options: PaginationOptions): Promise<RoleListDto> {
    try {
      this.logger.debug(`系统角色策略 - 查询所有角色，条件: ${JSON.stringify(where)}`);

      // 直接使用传入的查询条件，不再进行二次处理
      const filter = { ...where };

      this.logger.debug(`系统角色策略 - 构建的查询条件: ${JSON.stringify(filter)}`);

      // 查询总数
      const total = await this.prisma.systemRole.count({
        where: filter || undefined,
      });

      this.logger.debug(`系统角色策略 - 查询到的总数: ${total}`);

      // 查询角色列表
      const roles = await this.prisma.systemRole.findMany({
        where: filter || undefined,
        skip: options.skip,
        take: options.take,
        orderBy: {
          createdAt: 'desc',
        },
        include: {
          rolePermissions: {
            include: {
              permission: true,
            },
          },
          roleMenus: {
            include: {
              menu: true,
            },
          },
        },
      });

      this.logger.debug(`系统角色策略 - 查询到的角色数量: ${roles.length}`);

      // 转换返回结果
      const items = roles.map(role => ({
        id: role.id,
        name: role.name,
        code: role.code,
        remark: role.remark,
        status: role.status,
        createTime: DateFormatUtil.formatToDateTime(role.createdAt),
        updateTime: DateFormatUtil.formatToDateTime(role.updatedAt),
        permissions: role.rolePermissions.map(rp => rp.permission.id),
        menus: role.roleMenus.map(rm => rm.menu.id),
      }));

      const result = {
        items,
        total,
        page: options.page,
        pageSize: options.pageSize,
      };

      this.logger.debug(`系统角色策略 - 返回结果: ${JSON.stringify(result).substring(0, 200)}...`);

      return result;
    } catch (error) {
      this.logger.error(`查询角色列表失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 查询单个系统角色
   * @param id 角色ID
   * @returns 角色信息
   */
  async findOne(id: string): Promise<RoleDto> {
    try {
      const role = await this.prisma.systemRole.findUnique({
        where: { id },
        include: {
          rolePermissions: {
            include: {
              permission: true,
            },
          },
          roleMenus: {
            include: {
              menu: true,
            },
          },
        },
      });

      if (!role) {
        throw new NotFoundException(ApiMessage[ApiCode.ROLE_NOT_FOUND]);
      }

      // 转换返回结果
      return {
        id: role.id,
        name: role.name,
        code: role.code,
        remark: role.remark,
        status: role.status,
        createTime: DateFormatUtil.formatToDateTime(role.createdAt),
        updateTime: DateFormatUtil.formatToDateTime(role.updatedAt),
        permissions: role.rolePermissions.map(rp => rp.permission.id),
        menus: role.roleMenus.map(rm => rm.menu.id),
      };
    } catch (error) {
      this.logger.error(`查询角色失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 更新系统角色
   * @param id 角色ID
   * @param updateRoleDto 更新角色数据
   * @returns 更新后的角色
   */
  async update(id: string, updateRoleDto: UpdateRoleDto): Promise<RoleDto> {
    try {
      // 检查角色是否存在
      const existingRole = await this.prisma.systemRole.findUnique({
        where: { id },
      });

      if (!existingRole) {
        throw new NotFoundException(ApiMessage[ApiCode.ROLE_NOT_FOUND]);
      }

      // 不再检查角色编码唯一性

      // 更新角色基本信息
      const role = await this.prisma.systemRole.update({
        where: { id },
        data: {
          name: updateRoleDto.name,
          code: updateRoleDto.code,
          remark: updateRoleDto.remark,
          status: updateRoleDto.status,
        },
      });

      // 如果提供了权限ID列表，则更新角色权限关联
      if (updateRoleDto.permissionIds) {
        // 删除现有关联
        await this.prisma.systemRolePermission.deleteMany({
          where: { roleId: id },
        });

        // 创建新关联
        if (updateRoleDto.permissionIds.length > 0) {
          const rolePermissions = updateRoleDto.permissionIds.map(permissionId => ({
            roleId: id,
            permissionId,
          }));

          await this.prisma.systemRolePermission.createMany({
            data: rolePermissions,
          });
        }
      }

      // 如果提供了菜单ID列表，则更新角色菜单关联
      if (updateRoleDto.menuIds) {
        // 删除现有关联
        await this.prisma.systemRoleMenu.deleteMany({
          where: { roleId: id },
        });

        // 创建新关联
        if (updateRoleDto.menuIds.length > 0) {
          const roleMenus = updateRoleDto.menuIds.map(menuId => ({
            roleId: id,
            menuId,
          }));

          await this.prisma.systemRoleMenu.createMany({
            data: roleMenus,
          });
        }
      }

      this.logger.log(`更新系统角色成功: ${role.name}`);

      // 查询更新后的完整角色信息
      return this.findOne(id);
    } catch (error) {
      this.logger.error(`更新角色失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 删除系统角色
   * @param id 角色ID
   * @returns 删除结果
   */
  async remove(id: string): Promise<{ success: boolean }> {
    try {
      // 检查角色是否存在
      const existingRole = await this.prisma.systemRole.findUnique({
        where: { id },
      });

      if (!existingRole) {
        throw new NotFoundException(ApiMessage[ApiCode.ROLE_NOT_FOUND]);
      }

      // 删除角色
      await this.prisma.systemRole.delete({
        where: { id },
      });

      this.logger.log(`删除系统角色成功: ${existingRole.name}`);

      return { success: true };
    } catch (error) {
      this.logger.error(`删除角色失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 分配角色给系统用户
   * @param userId 用户ID
   * @param roleIds 角色ID列表
   * @returns 分配结果
   */
  async assignRolesToUser(userId: number, roleIds: string[]): Promise<{ success: boolean }> {
    try {
      // 检查用户是否存在
      const user = await this.prisma.systemUser.findUnique({
        where: { id: userId },
      });

      if (!user) {
        throw new NotFoundException(ApiMessage[ApiCode.USER_NOT_FOUND]);
      }

      // 删除现有用户角色关联
      await this.prisma.systemUserRole.deleteMany({
        where: { userId },
      });

      // 创建新的用户角色关联
      if (roleIds.length > 0) {
        const userRoles = roleIds.map(roleId => ({
          userId,
          roleId,
        }));

        await this.prisma.systemUserRole.createMany({
          data: userRoles,
        });
      }

      this.logger.log(`分配角色给系统用户成功: ${userId}`);

      return { success: true };
    } catch (error) {
      this.logger.error(`分配角色给用户失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 移除系统用户角色
   * @param userId 用户ID
   * @param roleId 角色ID
   * @returns 移除结果
   */
  async removeRoleFromUser(userId: number, roleId: string): Promise<{ success: boolean }> {
    try {
      // 检查用户角色关联是否存在
      const userRole = await this.prisma.systemUserRole.findFirst({
        where: {
          userId,
          roleId,
        },
      });

      if (!userRole) {
        throw new NotFoundException(ApiMessage[ApiCode.USER_ROLE_NOT_FOUND]);
      }

      // 删除用户角色关联
      await this.prisma.systemUserRole.deleteMany({
        where: {
          userId,
          roleId,
        },
      });

      this.logger.log(`移除系统用户角色成功: ${userId} - ${roleId}`);

      return { success: true };
    } catch (error) {
      this.logger.error(`移除用户角色失败: ${error.message}`);
      throw error;
    }
  }

  /**
   * 获取系统角色权限
   * @param roleId 角色ID
   * @returns 权限列表
   */
  async getRolePermissions(roleId: string): Promise<any[]> {
    try {
      // 检查角色是否存在
      const role = await this.prisma.systemRole.findUnique({
        where: { id: roleId },
      });

      if (!role) {
        throw new NotFoundException(ApiMessage[ApiCode.ROLE_NOT_FOUND]);
      }

      // 查询角色权限
      const rolePermissions = await this.prisma.systemRolePermission.findMany({
        where: { roleId },
        include: {
          permission: true,
        },
      });

      return rolePermissions.map(rp => rp.permission.id);
    } catch (error) {
      this.logger.error(`获取角色权限失败: ${error.message}`);
      throw error;
    }
  }
}
