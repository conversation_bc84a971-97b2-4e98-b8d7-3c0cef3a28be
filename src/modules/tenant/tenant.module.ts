import { Module } from '@nestjs/common';

import { TenantRegistrationController } from './controllers/tenant-registration.controller';
import { TenantRegistrationService } from './services/tenant-registration.service';
import { TenantController } from './tenant.controller';
import { TenantService } from './tenant.service';

import { PrismaModule } from '@/core/database/prisma/prisma.module';

/**
 * 租户模块
 * 处理租户相关的功能
 */
@Module({
  imports: [PrismaModule],
  controllers: [TenantController, TenantRegistrationController],
  providers: [TenantService, TenantRegistrationService],
  exports: [TenantService, TenantRegistrationService],
})
export class TenantModule {}
