import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import {
  IsString,
  IsOptional,
  IsNumber,
  IsBoolean,
  IsUrl,
  IsObject,
  ValidateNested,
  MinLength,
  MaxLength,
} from 'class-validator';

/**
 * 数据源配置DTO
 */
export class DatasourceConfigDto {
  @ApiPropertyOptional({ description: '数据库主机地址' })
  @IsOptional()
  @IsString()
  host?: string;

  @ApiPropertyOptional({ description: '数据库端口' })
  @IsOptional()
  @IsNumber()
  port?: number;

  @ApiPropertyOptional({ description: '数据库名称' })
  @IsOptional()
  @IsString()
  database?: string;

  @ApiPropertyOptional({ description: '数据库用户名' })
  @IsOptional()
  @IsString()
  username?: string;

  @ApiPropertyOptional({ description: '数据库密码' })
  @IsOptional()
  @IsString()
  password?: string;
}

/**
 * 创建租户DTO
 */
export class CreateTenantDto {
  @ApiProperty({ description: '租户代码', example: 'acme-corp' })
  @IsString()
  @MinLength(2)
  @MaxLength(50)
  code: string;

  @ApiProperty({ description: '租户名称', example: 'ACME Corporation' })
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  name: string;

  @ApiPropertyOptional({ description: '官网地址', example: 'https://acme.com' })
  @IsOptional()
  @IsUrl()
  website?: string;

  @ApiPropertyOptional({ description: '自定义域名', example: 'acme.example.com' })
  @IsOptional()
  @IsString()
  domain?: string;

  @ApiPropertyOptional({ description: '是否使用共享数据库', default: true })
  @IsOptional()
  @IsBoolean()
  isShared?: boolean;

  @ApiPropertyOptional({ description: '独立数据库配置（仅独立模式）' })
  @IsOptional()
  @IsObject()
  @ValidateNested()
  @Type(() => DatasourceConfigDto)
  datasourceConfig?: DatasourceConfigDto;
}

/**
 * 更新租户DTO
 */
export class UpdateTenantDto {
  @ApiPropertyOptional({ description: '租户名称' })
  @IsOptional()
  @IsString()
  @MinLength(1)
  @MaxLength(100)
  name?: string;

  @ApiPropertyOptional({ description: '官网地址' })
  @IsOptional()
  @IsUrl()
  website?: string;

  @ApiPropertyOptional({ description: '自定义域名' })
  @IsOptional()
  @IsString()
  domain?: string;

  @ApiPropertyOptional({ description: '租户状态', example: 1 })
  @IsOptional()
  @IsNumber()
  status?: number;
}

/**
 * 租户响应DTO
 */
export class TenantResponseDto {
  @ApiProperty({ description: '租户ID' })
  id: number;

  @ApiProperty({ description: '租户代码' })
  code: string;

  @ApiProperty({ description: '租户名称' })
  name: string;

  @ApiPropertyOptional({ description: '官网地址' })
  website?: string;

  @ApiPropertyOptional({ description: '自定义域名' })
  domain?: string;

  @ApiProperty({ description: '租户状态' })
  status: number;

  @ApiProperty({ description: '创建时间' })
  createTime: Date;

  @ApiProperty({ description: '更新时间' })
  updateTime: Date;

  @ApiPropertyOptional({ description: '数据源信息' })
  datasource?: {
    id: number;
    isShared: boolean;
    host?: string;
    port?: number;
    database?: string;
  };

  @ApiPropertyOptional({ description: '订阅信息' })
  subscriptions?: Array<{
    id: number;
    status: string;
    plan: {
      code: string;
      name: string;
      price: number;
    };
  }>;

  @ApiPropertyOptional({ description: '功能统计' })
  _count?: {
    features: number;
    configs: number;
  };
}

/**
 * 租户列表响应DTO
 */
export class TenantListResponseDto {
  @ApiProperty({ description: '租户列表', type: [TenantResponseDto] })
  data: TenantResponseDto[];

  @ApiProperty({ description: '总数' })
  total: number;
}

/**
 * 租户统计响应DTO
 */
export class TenantStatsResponseDto {
  @ApiProperty({ description: '总租户数' })
  total: number;

  @ApiProperty({ description: '活跃租户数' })
  active: number;

  @ApiProperty({ description: '停用租户数' })
  inactive: number;

  @ApiProperty({ description: '有自定义域名的租户数' })
  withDomain: number;

  @ApiProperty({ description: '共享数据库租户数' })
  sharedDatabase: number;

  @ApiProperty({ description: '独立数据库租户数' })
  dedicatedDatabase: number;
}
