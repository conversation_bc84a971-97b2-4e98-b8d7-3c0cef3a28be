import { ApiProperty } from '@nestjs/swagger';
import { JsonValue } from '@prisma/client/runtime/library';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsBoolean, IsInt, IsObject, IsArray, Min } from 'class-validator';

export class CreateComponentLibraryDto {
  @ApiProperty({ description: '组件名称' })
  @IsString()
  name: string;

  @ApiProperty({ description: '组件描述', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: '组件类型',
    examples: ['basic', 'layout', 'form', 'media', 'navigation', 'ecommerce'],
    default: 'basic',
  })
  @IsOptional()
  @IsString()
  type?: string = 'basic';

  @ApiProperty({
    description: '组件分类',
    examples: ['header', 'footer', 'hero', 'content', 'sidebar', 'button', 'form'],
    default: 'content',
  })
  @IsOptional()
  @IsString()
  category?: string = 'content';

  @ApiProperty({
    description: '组件标签',
    type: [String],
    required: false,
    example: ['响应式', '现代', '简洁'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[] = [];

  @ApiProperty({
    description: '组件配置架构',
    type: 'object',
    example: {
      type: 'object',
      properties: {
        title: { type: 'string', default: '标题' },
        content: { type: 'string', default: '内容' },
        buttonText: { type: 'string', default: '按钮' },
        backgroundColor: { type: 'string', default: '#ffffff' },
      },
      required: ['title'],
    },
  })
  @IsObject()
  schema: JsonValue;

  @ApiProperty({
    description: '默认属性配置',
    type: 'object',
    example: {
      title: '欢迎标题',
      content: '这里是内容描述',
      buttonText: '了解更多',
      backgroundColor: '#f8f9fa',
    },
  })
  @IsObject()
  defaultProps: JsonValue;

  @ApiProperty({
    description: '样式配置',
    type: 'object',
    required: false,
    example: {
      width: '100%',
      padding: '20px',
      borderRadius: '8px',
      boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
    },
  })
  @IsOptional()
  @IsObject()
  styleConfig?: JsonValue;

  @ApiProperty({ description: '组件预览图URL', required: false })
  @IsOptional()
  @IsString()
  previewImage?: string;

  @ApiProperty({ description: '组件图标', required: false })
  @IsOptional()
  @IsString()
  icon?: string;

  @ApiProperty({ description: '是否为系统组件', default: false })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isSystem?: boolean = false;

  @ApiProperty({ description: '是否激活', default: true })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isActive?: boolean = true;

  @ApiProperty({ description: '排序权重', default: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  sortOrder?: number = 0;

  @ApiProperty({ description: '版本号', default: '1.0.0' })
  @IsOptional()
  @IsString()
  version?: string = '1.0.0';

  @ApiProperty({
    description: '组件元数据',
    type: 'object',
    required: false,
    example: {
      author: '组件作者',
      documentation: 'https://docs.example.com',
      dependencies: [],
      responsive: true,
    },
  })
  @IsOptional()
  @IsObject()
  metadata?: JsonValue;
}
