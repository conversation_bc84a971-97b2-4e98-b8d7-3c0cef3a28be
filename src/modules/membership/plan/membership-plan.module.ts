import { Module } from '@nestjs/common';

import { MembershipPlanController } from './membership-plan.controller';
import { MembershipPlanService } from './membership-plan.service';
import { TenantMembershipPlanStrategy } from './strategies/tenant-membership-plan.strategy';

import { PrismaModule } from '@/core/database/prisma/prisma.module';

/**
 * 会员计划模块
 */
@Module({
  imports: [PrismaModule],
  controllers: [MembershipPlanController],
  providers: [MembershipPlanService, TenantMembershipPlanStrategy],
  exports: [MembershipPlanService],
})
export class MembershipPlanModule {}
