import { Injectable, Logger } from '@nestjs/common';

import { PublicDatabaseClient } from '@/core/database/clients/public.client';
import { DatabaseFactory } from '@/core/database/database.factory';

/**
 * 平台服务基类
 *
 * 为所有平台管理服务提供基础功能：
 * - Public Schema数据库访问
 * - 统一的日志记录
 * - 错误处理模式
 */
@Injectable()
export abstract class BasePlatformService {
  protected readonly logger: Logger;
  protected readonly publicDb: PublicDatabaseClient;

  constructor(
    private readonly databaseFactory: DatabaseFactory,
    serviceName: string,
  ) {
    this.publicDb = this.databaseFactory.getPublicClient();
    this.logger = new Logger(serviceName);
  }

  /**
   * 记录操作日志
   */
  protected logOperation(operation: string, data?: any) {
    this.logger.log(`${operation}${data ? `: ${JSON.stringify(data)}` : ''}`);
  }

  /**
   * 记录错误日志
   */
  protected logError(operation: string, error: Error) {
    this.logger.error(`${operation} failed: ${error.message}`, error.stack);
  }

  /**
   * 验证租户是否存在
   */
  protected async validateTenantExists(tenantId: number): Promise<boolean> {
    const tenant = await this.publicDb.tenant.findUnique({
      where: { id: tenantId },
    });
    return !!tenant;
  }

  /**
   * 获取租户基本信息
   */
  protected async getTenantInfo(tenantId: number) {
    return this.publicDb.tenant.findUnique({
      where: { id: tenantId },
      include: {
        datasource: true,
        subscriptions: {
          where: { status: 'active' },
          include: { plan: true },
          orderBy: { createdAt: 'desc' },
          take: 1,
        },
      },
    });
  }

  /**
   * 检查租户是否有指定功能权限
   */
  protected async checkTenantFeature(tenantId: number, featureCode: string): Promise<boolean> {
    const feature = await this.publicDb.tenantFeature.findFirst({
      where: {
        tenantId,
        featureCode,
        enabled: true,
      },
    });
    return !!feature;
  }
}
