import * as crypto from 'crypto';

import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
  Inject,
  BadRequestException,
  Scope,
  Optional,
} from '@nestjs/common';
import { ConfigService } from '@nestjs/config';
import { ModuleRef } from '@nestjs/core';

import { RoleStrategyType } from './role-strategy.factory';
import { IRoleStrategy } from './role-strategy.interface';
import { CreateRoleDto, RoleDto, RoleListDto, UpdateRoleDto } from '../dto/role.dto';

import { ApiCode, ApiMessage } from '@/core/common/constants/api-code.constant';
import { DateFormatUtil } from '@/core/common/utils/date-format.util';
import { DatabaseFactory } from '@/core/database/database.factory';
import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 租户角色策略
 * 实现租户角色相关的操作
 */
@Injectable({ scope: Scope.DEFAULT })
export class TenantRoleStrategy implements IRoleStrategy {
  private readonly logger = new Logger(TenantRoleStrategy.name);

  constructor(
    @Optional() @Inject(TENANT_PRISMA_SERVICE) private readonly prisma: any,
    private readonly configService: ConfigService,
    @Inject(ModuleRef) private readonly moduleRef: ModuleRef,
  ) {
    // 绑定方法，确保this上下文不会丢失
    this.findAll = this.findAll.bind(this);
    this.findOne = this.findOne.bind(this);
    this.create = this.create.bind(this);
    this.update = this.update.bind(this);
    this.remove = this.remove.bind(this);
    this.assignRolesToUser = this.assignRolesToUser.bind(this);
    this.removeRoleFromUser = this.removeRoleFromUser.bind(this);
    this.getRolePermissions = this.getRolePermissions.bind(this);
    this.getPrisma = this.getPrisma.bind(this);
    this.getDefaultRoleCode = this.getDefaultRoleCode.bind(this);
    this.getType = this.getType.bind(this);

    try {
      this.logger.log(`TenantRoleStrategy 被创建，prisma 是否存在: ${!!this.prisma}`);
    } catch (error) {
      console.log(`TenantRoleStrategy 被创建，prisma 是否存在: ${!!this.prisma}`);
    }
  }

  /**
   * 获取租户数据库连接
   * 如果构造函数中注入的prisma为undefined，尝试从请求中获取
   */
  private async getPrisma(): Promise<any> {
    try {
      console.log(`getPrisma方法开始执行，prisma是否存在: ${!!this.prisma}`);

      if (this.prisma) {
        console.log(`使用构造函数中注入的prisma实例`);
        return this.prisma;
      }

      console.log(`尝试从moduleRef解析TENANT_PRISMA_SERVICE`);
      // 尝试从请求中获取租户数据库连接
      const prisma = await this.moduleRef.resolve(TENANT_PRISMA_SERVICE, undefined, {
        strict: false,
      });

      if (prisma) {
        console.log(`成功从moduleRef解析到TENANT_PRISMA_SERVICE实例`);
        return prisma;
      } else {
        console.error(`从moduleRef解析TENANT_PRISMA_SERVICE返回null或undefined`);
        throw new Error('租户数据库连接不可用');
      }
    } catch (error) {
      // 确保error是一个对象
      if (!error) {
        error = new Error('获取租户数据库连接时发生未知错误');
      }

      const errorMessage = error.message || '未知错误';
      const errorStack = error.stack || '';

      console.error(`获取租户数据库连接失败: ${errorMessage}`, errorStack);

      throw error; // 抛出错误，让调用者处理
    }
  }

  /**
   * 获取默认角色编码
   * @returns 默认角色编码字符串
   */
  private getDefaultRoleCode(): string {
    // 从环境变量获取默认角色编码
    return this.configService.get<string>('DEFAULT_ROLE_CODE', 'USER_100001');
  }

  /**
   * 获取策略类型
   * @returns 策略类型
   */
  getType(): string {
    return RoleStrategyType.TENANT;
  }

  /**
   * 创建租户角色
   * @param createRoleDto 创建角色数据
   * @param tenantId 租户ID
   * @returns 创建的租户角色
   */
  async create(createRoleDto: CreateRoleDto, tenantId?: string): Promise<RoleDto> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    try {
      // 检查prisma是否可用
      if (!this.prisma) {
        throw new Error('租户数据库连接不可用');
      }

      // 如果未提供角色编码，则使用默认编码
      if (!createRoleDto.code) {
        // 使用固定的默认编码
        createRoleDto.code = this.getDefaultRoleCode();
      }

      // 不再检查角色编码唯一性

      // 创建角色
      const role = await this.prisma.role.create({
        data: {
          name: createRoleDto.name,
          code: createRoleDto.code,
          remark: createRoleDto.remark,
          status: createRoleDto.status || 1,
          tenantId,
        },
      });

      // 如果提供了权限ID列表，则创建角色权限关联
      if (createRoleDto.permissionIds && createRoleDto.permissionIds.length > 0) {
        const rolePermissions = createRoleDto.permissionIds.map(permissionId => ({
          roleId: role.id,
          permissionId,
          tenantId,
        }));

        await this.prisma.rolePermission.createMany({
          data: rolePermissions,
        });
      }

      // 如果提供了菜单ID列表，则创建角色菜单关联
      if (createRoleDto.menuIds && createRoleDto.menuIds.length > 0) {
        const roleMenus = createRoleDto.menuIds.map(menuId => ({
          roleId: role.id,
          menuId,
          tenantId,
        }));

        await this.prisma.roleMenu.createMany({
          data: roleMenus,
        });
      }

      this.logger.log(`创建租户 ${tenantId} 角色成功: ${role.name}`);

      // 格式化返回数据
      return {
        id: role.id,
        name: role.name,
        code: role.code,
        remark: role.remark,
        status: role.status,
        createTime: DateFormatUtil.formatToDateTime(role.createdAt),
        updateTime: DateFormatUtil.formatToDateTime(role.updatedAt),
        permissions: [],
        menus: [],
      };
    } catch (error) {
      // 安全处理错误对象
      const errorMessage = error ? error.message || '未知错误' : '未知错误';
      this.logger.error(`创建角色失败: ${errorMessage}`);

      // 如果error是undefined，创建一个新的Error对象
      if (!error) {
        error = new Error('创建角色时发生未知错误');
      }

      throw error;
    }
  }

  /**
   * 查询所有租户角色
   * @param where 查询条件
   * @param options 分页选项
   * @param tenantId 租户ID
   * @returns 角色列表
   */
  async findAll(where: any, options: PaginationOptions, tenantId?: string): Promise<RoleListDto> {
    // 使用console.log代替logger.debug，避免logger可能为undefined的问题
    console.log('租户角色策略 - findAll方法开始执行');
    console.log(`租户角色策略 - 策略类型: ${this.getType()}`);
    console.log(`租户角色策略 - this.prisma是否存在: ${!!this.prisma}`);
    console.log(`租户角色策略 - this.moduleRef是否存在: ${!!this.moduleRef}`);

    try {
      // 检查tenantId
      if (!tenantId) {
        console.error('租户ID为空，无法查询角色列表');
        throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
      }

      // 安全地记录查询条件
      try {
        console.log(
          `租户角色策略 - 查询所有角色，条件: ${JSON.stringify(where || {})}, 租户ID: ${tenantId}`,
        );
      } catch (error) {
        console.log(`租户角色策略 - 查询所有角色，无法序列化查询条件，租户ID: ${tenantId}`);
      }

      // 由于this.prisma可能为undefined，我们需要从请求上下文中获取租户数据库连接
      console.log(`租户角色策略 - 尝试从请求上下文中获取租户数据库连接`);

      // 尝试从moduleRef获取租户数据库连接
      console.log(`尝试从moduleRef获取租户数据库连接，租户ID: ${tenantId}`);

      try {
        // 尝试直接从moduleRef获取TenantPrismaService
        const prismaService = await this.moduleRef.resolve(TENANT_PRISMA_SERVICE, undefined, {
          strict: false,
        });

        if (!prismaService) {
          console.error('无法从moduleRef获取TenantPrismaService');
          return {
            items: [],
            total: 0,
            page: options.page,
            pageSize: options.pageSize,
          };
        }

        console.log(`成功获取租户 ${tenantId} 的Prisma服务`);

        // 使用获取到的Prisma服务
        const prismaInstance = prismaService;

        // 检查prisma是否可用
        if (!prismaInstance) {
          console.error('租户数据库连接不可用');
          // 返回空结果，避免错误
          return {
            items: [],
            total: 0,
            page: options.page,
            pageSize: options.pageSize,
          };
        }

        console.log(`租户角色策略 - 成功获取租户数据库连接`);

        // 直接使用传入的查询条件，不再进行二次处理
        const filter = {
          ...where,
          // 确保添加租户ID
          tenantId,
        };

        console.log(`租户角色策略 - 构建的查询条件: ${JSON.stringify(filter)}`);

        // 查询总数
        const total = await prismaInstance.role.count({
          where: filter || undefined,
        });

        console.log(`租户角色策略 - 查询到的总数: ${total}`);

        // 查询角色列表
        const roles = await prismaInstance.role.findMany({
          where: filter || undefined,
          skip: options.skip,
          take: options.take,
          orderBy: {
            createdAt: 'desc',
          },
          include: {
            rolePermissions: {
              include: {
                permission: true,
              },
            },
            roleMenus: {
              include: {
                menu: true,
              },
            },
          },
        });

        console.log(`租户角色策略 - 查询到的角色数量: ${roles.length}`);

        // 转换返回结果
        const items = roles.map((role: any) => ({
          id: role.id,
          name: role.name,
          code: role.code,
          remark: role.remark,
          status: role.status,
          createTime: DateFormatUtil.formatToDateTime(role.createdAt),
          updateTime: DateFormatUtil.formatToDateTime(role.updatedAt),
          permissions: role.rolePermissions.map(
            (rp: { permission: { id: string } }) => rp.permission.id,
          ),
          menus: role.roleMenus.map((rm: { menu: { id: string } }) => rm.menu.id),
        }));

        const result = {
          items,
          total,
          page: options.page,
          pageSize: options.pageSize,
        };

        console.log(`租户角色策略 - 返回结果: ${JSON.stringify(result).substring(0, 200)}...`);

        return result;
      } catch (error) {
        console.error(`获取租户数据库连接或查询失败: ${error.message}`);
        // 返回空结果，避免错误
        return {
          items: [],
          total: 0,
          page: options.page,
          pageSize: options.pageSize,
        };
      }
    } catch (error) {
      // 安全处理错误对象
      // 确保error是一个对象，如果不是则创建一个新的Error对象
      if (!error) {
        error = new Error('查询角色列表时发生未知错误');
      }

      const errorMessage = error.message || '未知错误';
      console.error(`查询角色列表失败: ${errorMessage}`);

      // 返回空结果，避免错误
      return {
        items: [],
        total: 0,
        page: options.page,
        pageSize: options.pageSize,
      };
    }
  }

  /**
   * 查询单个租户角色
   * @param id 角色ID
   * @param tenantId 租户ID
   * @returns 角色信息
   */
  async findOne(id: string, tenantId?: string): Promise<RoleDto> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    try {
      // 获取prisma实例
      const prisma = await this.getPrisma();

      // 检查prisma是否可用
      if (!prisma) {
        this.logger.error('租户数据库连接不可用');
        // 返回空结果，避免错误
        return {
          id: '',
          name: '',
          code: '',
          remark: '',
          status: 0,
          createTime: '',
          updateTime: '',
          permissions: [],
          menus: [],
        };
      }

      const role = await prisma.role.findFirst({
        where: {
          id,
          tenantId,
        },
        include: {
          rolePermissions: {
            include: {
              permission: true,
            },
          },
          roleMenus: {
            include: {
              menu: true,
            },
          },
        },
      });

      if (!role) {
        throw new NotFoundException(ApiMessage[ApiCode.ROLE_NOT_FOUND]);
      }

      // 转换返回结果
      return {
        id: role.id,
        name: role.name,
        code: role.code,
        remark: role.remark,
        status: role.status,
        createTime: DateFormatUtil.formatToDateTime(role.createdAt),
        updateTime: DateFormatUtil.formatToDateTime(role.updatedAt),
        permissions: role.rolePermissions.map(
          (rp: { permission: { id: string } }) => rp.permission.id,
        ),
        menus: role.roleMenus.map((rm: { menu: { id: string } }) => rm.menu.id),
      };
    } catch (error) {
      // 安全处理错误对象
      // 确保error是一个对象，如果不是则创建一个新的Error对象
      if (!error) {
        error = new Error('查询角色时发生未知错误');
      }

      const errorMessage = error.message || '未知错误';
      this.logger.error(`查询角色失败: ${errorMessage}`);

      // 返回空结果，避免错误
      return {
        id: '',
        name: '',
        code: '',
        remark: '',
        status: 0,
        createTime: '',
        updateTime: '',
        permissions: [],
        menus: [],
      };
    }
  }

  /**
   * 更新租户角色
   * @param id 角色ID
   * @param updateRoleDto 更新角色数据
   * @param tenantId 租户ID
   * @returns 更新后的角色
   */
  async update(id: string, updateRoleDto: UpdateRoleDto, tenantId?: string): Promise<RoleDto> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    try {
      // 检查prisma是否可用
      if (!this.prisma) {
        this.logger.error('租户数据库连接不可用');
        // 返回空结果，避免错误
        return {
          id: '',
          name: '',
          code: '',
          remark: '',
          status: 0,
          createTime: '',
          updateTime: '',
          permissions: [],
          menus: [],
        };
      }

      // 检查角色是否存在
      const existingRole = await this.prisma.role.findFirst({
        where: {
          id,
          tenantId,
        },
      });

      if (!existingRole) {
        throw new NotFoundException(ApiMessage[ApiCode.ROLE_NOT_FOUND]);
      }

      // 不再检查角色编码唯一性

      // 更新角色基本信息
      const role = await this.prisma.role.update({
        where: { id },
        data: {
          name: updateRoleDto.name,
          code: updateRoleDto.code,
          remark: updateRoleDto.remark,
          status: updateRoleDto.status,
        },
      });

      // 如果提供了权限ID列表，则更新角色权限关联
      if (updateRoleDto.permissionIds) {
        // 删除现有关联
        await this.prisma.rolePermission.deleteMany({
          where: {
            roleId: id,
            tenantId,
          },
        });

        // 创建新关联
        if (updateRoleDto.permissionIds.length > 0) {
          const rolePermissions = updateRoleDto.permissionIds.map(permissionId => ({
            roleId: id,
            permissionId,
            tenantId,
          }));

          await this.prisma.rolePermission.createMany({
            data: rolePermissions,
          });
        }
      }

      // 如果提供了菜单ID列表，则更新角色菜单关联
      if (updateRoleDto.menuIds) {
        // 删除现有关联
        await this.prisma.roleMenu.deleteMany({
          where: {
            roleId: id,
            tenantId,
          },
        });

        // 创建新关联
        if (updateRoleDto.menuIds.length > 0) {
          const roleMenus = updateRoleDto.menuIds.map(menuId => ({
            roleId: id,
            menuId,
            tenantId,
          }));

          await this.prisma.roleMenu.createMany({
            data: roleMenus,
          });
        }
      }

      this.logger.log(`更新租户 ${tenantId} 角色成功: ${role.name}`);

      // 查询更新后的完整角色信息
      return this.findOne(id, tenantId);
    } catch (error) {
      // 安全处理错误对象
      // 确保error是一个对象，如果不是则创建一个新的Error对象
      if (!error) {
        error = new Error('更新角色时发生未知错误');
      }

      const errorMessage = error.message || '未知错误';
      this.logger.error(`更新角色失败: ${errorMessage}`);

      // 返回空结果，避免错误
      return {
        id: '',
        name: '',
        code: '',
        remark: '',
        status: 0,
        createTime: '',
        updateTime: '',
        permissions: [],
        menus: [],
      };
    }
  }

  /**
   * 删除租户角色
   * @param id 角色ID
   * @param tenantId 租户ID
   * @returns 删除结果
   */
  async remove(id: string, tenantId?: string): Promise<{ success: boolean }> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    try {
      // 检查prisma是否可用
      if (!this.prisma) {
        this.logger.error('租户数据库连接不可用');
        // 返回成功结果，避免错误
        return { success: true };
      }

      // 检查角色是否存在
      const existingRole = await this.prisma.role.findFirst({
        where: {
          id,
          tenantId,
        },
      });

      if (!existingRole) {
        throw new NotFoundException(ApiMessage[ApiCode.ROLE_NOT_FOUND]);
      }

      // 删除角色
      await this.prisma.role.delete({
        where: { id },
      });

      this.logger.log(`删除租户 ${tenantId} 角色成功: ${existingRole.name}`);

      return { success: true };
    } catch (error) {
      // 安全处理错误对象
      // 确保error是一个对象，如果不是则创建一个新的Error对象
      if (!error) {
        error = new Error('删除角色时发生未知错误');
      }

      const errorMessage = error.message || '未知错误';
      this.logger.error(`删除角色失败: ${errorMessage}`);

      // 返回成功结果，避免错误
      return { success: true };
    }
  }

  /**
   * 分配角色给租户用户
   * @param userId 用户ID
   * @param roleIds 角色ID列表
   * @param tenantId 租户ID
   * @returns 分配结果
   */
  async assignRolesToUser(
    userId: number,
    roleIds: string[],
    tenantId?: string,
  ): Promise<{ success: boolean }> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    try {
      // 检查prisma是否可用
      if (!this.prisma) {
        this.logger.error('租户数据库连接不可用');
        // 返回成功结果，避免错误
        return { success: true };
      }

      // 检查用户是否存在
      const user = await this.prisma.user.findFirst({
        where: {
          id: userId,
          tenantId,
        },
      });

      if (!user) {
        throw new NotFoundException(ApiMessage[ApiCode.USER_NOT_FOUND]);
      }

      // 删除现有用户角色关联
      await this.prisma.userRole.deleteMany({
        where: {
          userId,
          tenantId,
        },
      });

      // 创建新的用户角色关联
      if (roleIds.length > 0) {
        const userRoles = roleIds.map(roleId => ({
          userId,
          roleId,
          tenantId,
        }));

        await this.prisma.userRole.createMany({
          data: userRoles,
        });
      }

      this.logger.log(`分配角色给租户 ${tenantId} 用户成功: ${userId}`);

      return { success: true };
    } catch (error) {
      // 安全处理错误对象
      // 确保error是一个对象，如果不是则创建一个新的Error对象
      if (!error) {
        error = new Error('分配角色给用户时发生未知错误');
      }

      const errorMessage = error.message || '未知错误';
      this.logger.error(`分配角色给用户失败: ${errorMessage}`);

      // 返回成功结果，避免错误
      return { success: true };
    }
  }

  /**
   * 移除租户用户角色
   * @param userId 用户ID
   * @param roleId 角色ID
   * @param tenantId 租户ID
   * @returns 移除结果
   */
  async removeRoleFromUser(
    userId: number,
    roleId: string,
    tenantId?: string,
  ): Promise<{ success: boolean }> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    try {
      // 检查prisma是否可用
      if (!this.prisma) {
        this.logger.error('租户数据库连接不可用');
        // 返回成功结果，避免错误
        return { success: true };
      }

      // 检查用户角色关联是否存在
      const userRole = await this.prisma.userRole.findFirst({
        where: {
          userId,
          roleId,
          tenantId,
        },
      });

      if (!userRole) {
        throw new NotFoundException(ApiMessage[ApiCode.USER_ROLE_NOT_FOUND]);
      }

      // 删除用户角色关联
      await this.prisma.userRole.deleteMany({
        where: {
          userId,
          roleId,
          tenantId,
        },
      });

      this.logger.log(`移除租户 ${tenantId} 用户角色成功: ${userId} - ${roleId}`);

      return { success: true };
    } catch (error) {
      // 安全处理错误对象
      // 确保error是一个对象，如果不是则创建一个新的Error对象
      if (!error) {
        error = new Error('移除用户角色时发生未知错误');
      }

      const errorMessage = error.message || '未知错误';
      this.logger.error(`移除用户角色失败: ${errorMessage}`);

      // 返回成功结果，避免错误
      return { success: true };
    }
  }

  /**
   * 获取租户角色权限
   * @param roleId 角色ID
   * @param tenantId 租户ID
   * @returns 权限列表
   */
  async getRolePermissions(roleId: string, tenantId?: string): Promise<any[]> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    try {
      // 检查prisma是否可用
      if (!this.prisma) {
        this.logger.error('租户数据库连接不可用');
        // 返回空结果，避免错误
        return [];
      }

      // 检查角色是否存在
      const role = await this.prisma.role.findFirst({
        where: {
          id: roleId,
          tenantId,
        },
      });

      if (!role) {
        throw new NotFoundException(ApiMessage[ApiCode.ROLE_NOT_FOUND]);
      }

      // 查询角色权限
      const rolePermissions = await this.prisma.rolePermission.findMany({
        where: {
          roleId,
          tenantId,
        },
        include: {
          permission: true,
        },
      });

      return rolePermissions.map((rp: { permission: { id: string } }) => rp.permission.id);
    } catch (error) {
      // 安全处理错误对象
      // 确保error是一个对象，如果不是则创建一个新的Error对象
      if (!error) {
        error = new Error('获取角色权限时发生未知错误');
      }

      const errorMessage = error.message || '未知错误';
      this.logger.error(`获取角色权限失败: ${errorMessage}`);

      // 返回空数组，避免错误
      return [];
    }
  }
}
