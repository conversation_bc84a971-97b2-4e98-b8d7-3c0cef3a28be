import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsDate, IsInt, IsOptional, Min, Max, IsString, IsEnum } from 'class-validator';

/**
 * 基础响应DTO
 */
export abstract class BaseResponseDto {
  @ApiProperty({ description: 'ID' })
  id: number;

  @ApiProperty({ description: '创建时间' })
  @IsDate()
  @Type(() => Date)
  createTime: Date;

  @ApiProperty({ description: '更新时间' })
  @IsDate()
  @Type(() => Date)
  updateTime: Date;
}

/**
 * 租户响应DTO基类
 */
export abstract class BaseTenantResponseDto extends BaseResponseDto {
  @ApiProperty({ description: '租户ID' })
  tenantId: number;
}

/**
 * 分页查询DTO基类
 */
export class BaseQueryDto {
  @ApiPropertyOptional({ description: '页码', default: 1, minimum: 1 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  page?: number = 1;

  @ApiPropertyOptional({ description: '每页数量', default: 10, minimum: 1, maximum: 100 })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Max(100)
  @Type(() => Number)
  limit?: number = 10;

  @ApiPropertyOptional({ description: '搜索关键词' })
  @IsOptional()
  @IsString()
  search?: string;

  @ApiPropertyOptional({
    description: '排序字段',
    default: 'createTime',
    examples: ['createTime', 'updateTime', 'name', 'id'],
  })
  @IsOptional()
  @IsString()
  sortBy?: string = 'createTime';

  @ApiPropertyOptional({
    description: '排序方向',
    enum: ['asc', 'desc'],
    default: 'desc',
  })
  @IsOptional()
  @IsEnum(['asc', 'desc'])
  sortOrder?: 'asc' | 'desc' = 'desc';
}

/**
 * 租户查询DTO基类
 */
export class BaseTenantQueryDto extends BaseQueryDto {
  @ApiPropertyOptional({ description: '租户ID过滤' })
  @IsOptional()
  @IsInt()
  @Type(() => Number)
  tenantId?: number;
}

/**
 * 分页响应DTO基类
 */
export class BaseListResponseDto<T> {
  @ApiProperty({ description: '数据列表', type: [Object] })
  items: T[];

  @ApiProperty({ description: '总记录数' })
  total: number;

  @ApiProperty({ description: '当前页码' })
  page: number;

  @ApiProperty({ description: '每页数量' })
  limit: number;

  @ApiProperty({ description: '总页数' })
  totalPages: number;

  @ApiProperty({ description: '是否有下一页' })
  hasNext: boolean;

  @ApiProperty({ description: '是否有上一页' })
  hasPrev: boolean;
}
