import { CurrentTenant } from '@core/decorators/current-tenant.decorator';
import { CurrentUser } from '@core/decorators/current-user.decorator';
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
  ApiBody,
} from '@nestjs/swagger';

import { CreateMediaAssetDto } from './dto/create-media-asset.dto';
import { QueryMediaAssetDto } from './dto/query-media-asset.dto';
import { UpdateMediaAssetDto } from './dto/update-media-asset.dto';
import { MediaAssetEntity } from './entities/media-asset.entity';
import { MediaAssetService } from './media-asset.service';

@ApiTags('媒体资源管理')
@ApiBearerAuth()
@Controller('media-assets')
export class MediaAssetController {
  constructor(private readonly mediaAssetService: MediaAssetService) {}

  @Post()
  @ApiOperation({ summary: '创建媒体资源' })
  @ApiResponse({
    status: 201,
    description: '媒体资源创建成功',
    type: MediaAssetEntity,
  })
  @ApiResponse({ status: 400, description: '网站不存在或请求参数错误' })
  async create(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Body() createDto: CreateMediaAssetDto,
  ): Promise<MediaAssetEntity> {
    return this.mediaAssetService.create(tenantId, userId, createDto);
  }

  @Get()
  @ApiOperation({ summary: '获取媒体资源列表' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/MediaAssetEntity' },
        },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
        totalPages: { type: 'number' },
      },
    },
  })
  async findAll(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Query() queryDto: QueryMediaAssetDto,
  ) {
    return this.mediaAssetService.findAll(tenantId, userId, queryDto);
  }

  @Get('stats')
  @ApiOperation({ summary: '获取媒体资源统计信息' })
  @ApiQuery({ name: 'websiteId', required: false, description: '网站ID过滤' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        total: { type: 'number' },
        byCategory: {
          type: 'object',
          properties: {
            images: { type: 'number' },
            videos: { type: 'number' },
            documents: { type: 'number' },
            audio: { type: 'number' },
          },
        },
        totalSize: { type: 'number' },
      },
    },
  })
  async getStats(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Query('websiteId', ParseIntPipe) websiteId?: number,
  ) {
    return this.mediaAssetService.getMediaStats(tenantId, websiteId, userId);
  }

  @Get('categories')
  @ApiOperation({ summary: '获取媒体分类列表' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'array',
      items: { type: 'string' },
    },
  })
  async getCategories(@CurrentTenant('id') tenantId: number) {
    return this.mediaAssetService.getCategories(tenantId);
  }

  @Get('tags')
  @ApiOperation({ summary: '获取所有标签' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'array',
      items: { type: 'string' },
    },
  })
  async getTags(@CurrentTenant('id') tenantId: number) {
    return this.mediaAssetService.getTags(tenantId);
  }

  @Get('by-website/:websiteId')
  @ApiOperation({ summary: '获取指定网站的媒体资源' })
  @ApiParam({ name: 'websiteId', description: '网站ID' })
  @ApiQuery({ name: 'category', required: false, description: '媒体分类过滤' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'number' },
          filename: { type: 'string' },
          originalName: { type: 'string' },
          url: { type: 'string' },
          thumbnailUrl: { type: 'string' },
          mimeType: { type: 'string' },
          category: { type: 'string' },
          size: { type: 'number' },
          alt: { type: 'string' },
          tags: { type: 'array', items: { type: 'string' } },
          createTime: { type: 'string' },
        },
      },
    },
  })
  async getMediaByWebsite(
    @CurrentTenant('id') tenantId: number,
    @Param('websiteId', ParseIntPipe) websiteId: number,
    @Query('category') category?: string,
  ) {
    return this.mediaAssetService.getMediaByWebsite(tenantId, websiteId, category);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取单个媒体资源详情' })
  @ApiParam({ name: 'id', description: '媒体资源ID' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: MediaAssetEntity,
  })
  @ApiResponse({ status: 404, description: '媒体资源不存在' })
  async findOne(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<MediaAssetEntity> {
    return this.mediaAssetService.findOne(tenantId, id, userId);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新媒体资源信息' })
  @ApiParam({ name: 'id', description: '媒体资源ID' })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    type: MediaAssetEntity,
  })
  @ApiResponse({ status: 404, description: '媒体资源不存在' })
  @ApiResponse({ status: 400, description: '网站不存在' })
  async update(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateMediaAssetDto,
  ): Promise<MediaAssetEntity> {
    return this.mediaAssetService.update(tenantId, id, updateDto, userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除媒体资源' })
  @ApiParam({ name: 'id', description: '媒体资源ID' })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiResponse({ status: 404, description: '媒体资源不存在' })
  async remove(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<{ message: string }> {
    await this.mediaAssetService.remove(tenantId, id, userId);
    return { message: '媒体资源删除成功' };
  }

  @Post('move-to-website')
  @ApiOperation({ summary: '移动媒体资源到指定网站' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        mediaIds: {
          type: 'array',
          items: { type: 'number' },
          description: '媒体资源ID数组',
        },
        websiteId: {
          type: 'number',
          description: '目标网站ID',
        },
      },
      required: ['mediaIds', 'websiteId'],
    },
  })
  @ApiResponse({ status: 200, description: '移动成功' })
  @ApiResponse({ status: 400, description: '网站不存在' })
  async moveToWebsite(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Body() body: { mediaIds: number[]; websiteId: number },
  ): Promise<{ message: string }> {
    await this.mediaAssetService.moveToWebsite(tenantId, body.mediaIds, body.websiteId, userId);
    return { message: '媒体资源移动成功' };
  }

  @Post(':id/tags')
  @ApiOperation({ summary: '添加标签' })
  @ApiParam({ name: 'id', description: '媒体资源ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        tags: {
          type: 'array',
          items: { type: 'string' },
          description: '要添加的标签数组',
        },
      },
      required: ['tags'],
    },
  })
  @ApiResponse({
    status: 200,
    description: '添加成功',
    type: MediaAssetEntity,
  })
  @ApiResponse({ status: 404, description: '媒体资源不存在' })
  async addTags(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Param('id', ParseIntPipe) id: number,
    @Body() body: { tags: string[] },
  ): Promise<MediaAssetEntity> {
    return this.mediaAssetService.addTags(tenantId, id, body.tags, userId);
  }

  @Delete(':id/tags')
  @ApiOperation({ summary: '移除标签' })
  @ApiParam({ name: 'id', description: '媒体资源ID' })
  @ApiBody({
    schema: {
      type: 'object',
      properties: {
        tags: {
          type: 'array',
          items: { type: 'string' },
          description: '要移除的标签数组',
        },
      },
      required: ['tags'],
    },
  })
  @ApiResponse({
    status: 200,
    description: '移除成功',
    type: MediaAssetEntity,
  })
  @ApiResponse({ status: 404, description: '媒体资源不存在' })
  async removeTags(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Param('id', ParseIntPipe) id: number,
    @Body() body: { tags: string[] },
  ): Promise<MediaAssetEntity> {
    return this.mediaAssetService.removeTags(tenantId, id, body.tags, userId);
  }
}
