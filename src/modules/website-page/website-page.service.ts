import {
  Injectable,
  NotFoundException,
  ConflictException,
  BadRequestException,
  Inject,
} from '@nestjs/common';

import { CreateWebsitePageDto } from './dto/create-website-page.dto';
import { QueryWebsitePageDto } from './dto/query-website-page.dto';
import { UpdateWebsitePageDto } from './dto/update-website-page.dto';
import { WebsitePageEntity } from './entities/website-page.entity';

import { DatabaseFactory } from '@/core/database/database.factory';
import { BaseBusinessService } from '@/modules/business/base/business.service';

@Injectable()
export class WebsitePageService extends BaseBusinessService {
  constructor(databaseFactory: DatabaseFactory) {
    super(databaseFactory, 'WebsitePageService');
  }

  async create(tenantId: number, createDto: CreateWebsitePageDto): Promise<WebsitePageEntity> {
    // 检查网站是否存在且属于当前租户
    const website = await this.tenantDb.website.findFirst({
      where: {
        id: createDto.websiteId,
        tenantId,
      },
    });

    if (!website) {
      throw new BadRequestException(`网站 ID ${createDto.websiteId} 不存在`);
    }

    // 检查同一网站下页面路径是否已存在
    const existingPage = await this.tenantDb.websitePage.findFirst({
      where: {
        slug: createDto.slug,
        websiteId: createDto.websiteId,
        tenantId,
      },
    });

    if (existingPage) {
      throw new ConflictException(`页面路径 "${createDto.slug}" 在该网站下已存在`);
    }

    // 如果设置为首页，需要将其他页面的首页标识取消
    if (createDto.isHomePage) {
      await this.updateHomePage(tenantId, createDto.websiteId, null);
    }

    const page = await this.tenantDb.websitePage.create({
      data: {
        ...createDto,
        tenantId,
        status: 'draft',
      },
      include: {
        website: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
    });

    return page;
  }

  async findAll(tenantId: number, queryDto: QueryWebsitePageDto = {}) {
    const {
      page = 1,
      limit = 10,
      keyword,
      status,
      type,
      websiteId,
      isHomePage,
      sortBy = 'sortOrder',
      sortOrder = 'asc',
      includeRelations = false,
    } = queryDto;

    const skip = (page - 1) * limit;
    const take = limit;

    // 构建查询条件
    const where: any = {
      tenantId,
    };

    if (keyword) {
      where.OR = [
        { title: { contains: keyword, mode: 'insensitive' } },
        { slug: { contains: keyword, mode: 'insensitive' } },
        { seoTitle: { contains: keyword, mode: 'insensitive' } },
        { seoDescription: { contains: keyword, mode: 'insensitive' } },
      ];
    }

    if (status) {
      where.status = status;
    }

    if (type) {
      where.type = type;
    }

    if (websiteId) {
      where.websiteId = websiteId;
    }

    if (isHomePage !== undefined) {
      where.isHomePage = isHomePage;
    }

    // 构建排序条件
    const orderBy: any = {};
    orderBy[sortBy] = sortOrder;

    // 构建包含关系
    const include = includeRelations
      ? {
          website: {
            select: {
              id: true,
              name: true,
              status: true,
              subdomain: true,
              domain: true,
            },
          },
        }
      : undefined;

    const [pages, total] = await Promise.all([
      this.tenantDb.websitePage.findMany({
        where,
        skip,
        take,
        orderBy,
        include,
      }),
      this.tenantDb.websitePage.count({ where }),
    ]);

    return {
      data: pages,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(tenantId: number, id: number): Promise<WebsitePageEntity> {
    const page = await this.tenantDb.websitePage.findFirst({
      where: {
        id,
        tenantId,
      },
      include: {
        website: {
          select: {
            id: true,
            name: true,
            status: true,
            subdomain: true,
            domain: true,
          },
        },
      },
    });

    if (!page) {
      throw new NotFoundException(`页面 ID ${id} 不存在`);
    }

    return page;
  }

  async findBySlug(tenantId: number, websiteId: number, slug: string): Promise<WebsitePageEntity> {
    const page = await this.tenantDb.websitePage.findFirst({
      where: {
        slug,
        websiteId,
        tenantId,
      },
      include: {
        website: {
          select: {
            id: true,
            name: true,
            status: true,
            subdomain: true,
            domain: true,
          },
        },
      },
    });

    if (!page) {
      throw new NotFoundException(`页面路径 "${slug}" 不存在`);
    }

    return page;
  }

  async update(
    tenantId: number,
    id: number,
    updateDto: UpdateWebsitePageDto,
  ): Promise<WebsitePageEntity> {
    await this.findOne(tenantId, id); // 检查页面是否存在

    // 检查路径冲突
    if (updateDto.slug) {
      const existingPage = await this.tenantDb.websitePage.findFirst({
        where: {
          slug: updateDto.slug,
          websiteId: updateDto.websiteId,
          tenantId,
          NOT: {
            id,
          },
        },
      });

      if (existingPage) {
        throw new ConflictException(`页面路径 "${updateDto.slug}" 在该网站下已被其他页面使用`);
      }
    }

    // 如果设置为首页，需要将其他页面的首页标识取消
    if (updateDto.isHomePage && updateDto.websiteId) {
      await this.updateHomePage(tenantId, updateDto.websiteId, id);
    }

    const page = await this.tenantDb.websitePage.update({
      where: {
        id,
        tenantId,
      },
      data: updateDto,
      include: {
        website: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
    });

    return page;
  }

  async remove(tenantId: number, id: number): Promise<void> {
    await this.findOne(tenantId, id); // 检查页面是否存在

    await this.tenantDb.websitePage.delete({
      where: {
        id,
        tenantId,
      },
    });
  }

  async publish(tenantId: number, id: number): Promise<WebsitePageEntity> {
    const page = await this.findOne(tenantId, id);

    if (page.status === 'published') {
      throw new BadRequestException('页面已经是发布状态');
    }

    const updatedPage = await this.tenantDb.websitePage.update({
      where: {
        id,
        tenantId,
      },
      data: {
        status: 'published',
      },
      include: {
        website: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
    });

    return updatedPage;
  }

  async unpublish(tenantId: number, id: number): Promise<WebsitePageEntity> {
    const page = await this.findOne(tenantId, id);

    if (page.status !== 'published') {
      throw new BadRequestException('页面未处于发布状态');
    }

    const updatedPage = await this.tenantDb.websitePage.update({
      where: {
        id,
        tenantId,
      },
      data: {
        status: 'draft',
      },
      include: {
        website: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
    });

    return updatedPage;
  }

  async duplicate(tenantId: number, id: number, newSlug: string): Promise<WebsitePageEntity> {
    const originalPage = await this.findOne(tenantId, id);

    // 检查新路径是否已存在
    const existingPage = await this.tenantDb.websitePage.findFirst({
      where: {
        slug: newSlug,
        websiteId: originalPage.websiteId,
        tenantId,
      },
    });

    if (existingPage) {
      throw new ConflictException(`页面路径 "${newSlug}" 已存在`);
    }

    const duplicatedPage = await this.tenantDb.websitePage.create({
      data: {
        title: `${originalPage.title} (副本)`,
        slug: newSlug,
        content: originalPage.content,
        type: originalPage.type,
        seoTitle: originalPage.seoTitle,
        seoDescription: originalPage.seoDescription,
        seoKeywords: originalPage.seoKeywords,
        customCSS: originalPage.customCSS,
        customJS: originalPage.customJS,
        sortOrder: 0,
        isHomePage: false, // 副本不设为首页
        metadata: originalPage.metadata,
        websiteId: originalPage.websiteId,
        tenantId,
        status: 'draft',
      },
      include: {
        website: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
      },
    });

    return duplicatedPage;
  }

  async getPagesByWebsite(tenantId: number, websiteId: number) {
    const pages = await this.tenantDb.websitePage.findMany({
      where: {
        websiteId,
        tenantId,
      },
      orderBy: [{ isHomePage: 'desc' }, { sortOrder: 'asc' }, { createTime: 'asc' }],
      select: {
        id: true,
        title: true,
        slug: true,
        type: true,
        status: true,
        isHomePage: true,
        sortOrder: true,
        createTime: true,
        updateTime: true,
      },
    });

    return pages;
  }

  async getPageStats(tenantId: number, websiteId?: number) {
    const where: any = {
      tenantId,
    };

    if (websiteId) {
      where.websiteId = websiteId;
    }

    const [total, published, draft] = await Promise.all([
      this.tenantDb.websitePage.count({ where }),
      this.tenantDb.websitePage.count({ where: { ...where, status: 'published' } }),
      this.tenantDb.websitePage.count({ where: { ...where, status: 'draft' } }),
    ]);

    return {
      total,
      published,
      draft,
    };
  }

  private async updateHomePage(
    tenantId: number,
    websiteId: number,
    excludePageId?: number,
  ): Promise<void> {
    const where: any = {
      websiteId,
      tenantId,
      isHomePage: true,
    };

    if (excludePageId) {
      where.NOT = { id: excludePageId };
    }

    await this.tenantDb.websitePage.updateMany({
      where,
      data: {
        isHomePage: false,
      },
    });
  }

  // 基础服务方法暂时留空，需要根据实际Schema调整
  async createPage(pageData: any) {
    throw new Error('Method not implemented - Schema fields need to be verified');
  }

  async getPageList(query: any) {
    throw new Error('Method not implemented - Schema fields need to be verified');
  }

  async getPageById(id: number) {
    throw new Error('Method not implemented - Schema fields need to be verified');
  }

  async updatePage(id: number, updateData: any) {
    throw new Error('Method not implemented - Schema fields need to be verified');
  }

  async deletePage(id: number) {
    throw new Error('Method not implemented - Schema fields need to be verified');
  }
}
