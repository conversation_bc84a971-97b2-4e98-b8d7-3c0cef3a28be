import { PrismaModule } from '@core/database/prisma/prisma.module';
import { Module } from '@nestjs/common';

import { ComponentLibraryController } from './component-library.controller';
import { ComponentLibraryService } from './component-library.service';

@Module({
  imports: [PrismaModule],
  controllers: [ComponentLibraryController],
  providers: [ComponentLibraryService],
  exports: [ComponentLibraryService],
})
export class ComponentLibraryModule {}
