import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { Injectable, Logger, Inject } from '@nestjs/common';
import { Cache } from 'cache-manager';

import { DatabaseFactory } from '@/core/database/database.factory';

/**
 * 租户配置缓存服务
 *
 * 负责缓存租户相关的配置和上下文信息：
 * - 租户基本信息
 * - 订阅计划信息
 * - 功能权限配置
 * - 系统配置
 */
@Injectable()
export class TenantConfigCache {
  private readonly logger = new Logger(TenantConfigCache.name);

  constructor(
    @Inject(CACHE_MANAGER) private cacheManager: Cache,
    private readonly databaseFactory: DatabaseFactory,
  ) {}

  /**
   * 获取租户完整配置
   * 包含订阅、功能、配置等信息
   */
  async getTenantConfig(tenantId: number) {
    const cacheKey = `tenant:${tenantId}:config`;

    try {
      // 尝试从缓存获取
      let config = await this.cacheManager.get(cacheKey);
      if (config) {
        this.logger.debug(`租户配置缓存命中: ${tenantId}`);
        return config;
      }

      // 缓存未命中，从数据库获取
      config = await this.fetchTenantConfigFromDb(tenantId);

      // 缓存1小时
      await this.cacheManager.set(cacheKey, config, 3600);
      this.logger.debug(`租户配置已缓存: ${tenantId}`);

      return config;
    } catch (error) {
      this.logger.error(`获取租户配置失败: ${tenantId}`, error);
      throw error;
    }
  }

  /**
   * 获取租户基本信息
   */
  async getTenantInfo(tenantId: number) {
    const cacheKey = `tenant:${tenantId}:info`;

    try {
      let info = await this.cacheManager.get(cacheKey);
      if (info) {
        return info;
      }

      const publicDb = this.databaseFactory.getPublicClient();
      info = await publicDb.tenant.findUnique({
        where: { id: tenantId },
        select: {
          id: true,
          code: true,
          name: true,
          domain: true,
          status: true,
        },
      });

      if (info) {
        // 缓存30分钟
        await this.cacheManager.set(cacheKey, info, 1800);
      }

      return info;
    } catch (error) {
      this.logger.error(`获取租户信息失败: ${tenantId}`, error);
      throw error;
    }
  }

  /**
   * 获取租户功能权限
   */
  async getTenantFeatures(tenantId: number) {
    const cacheKey = `tenant:${tenantId}:features`;

    try {
      let features = await this.cacheManager.get(cacheKey);
      if (features) {
        return features;
      }

      const publicDb = this.databaseFactory.getPublicClient();
      features = await publicDb.tenantFeature.findMany({
        where: {
          tenantId,
          enabled: true,
        },
        select: {
          featureCode: true,
          quota: true,
          usedQuota: true,
        },
      });

      // 缓存15分钟
      await this.cacheManager.set(cacheKey, features, 900);

      return features;
    } catch (error) {
      this.logger.error(`获取租户功能失败: ${tenantId}`, error);
      throw error;
    }
  }

  /**
   * 检查租户是否有指定功能
   */
  async hasTenantFeature(tenantId: number, featureCode: string): Promise<boolean> {
    const features = await this.getTenantFeatures(tenantId);
    return Array.isArray(features) && features.some((f: any) => f.featureCode === featureCode);
  }

  /**
   * 获取租户配置项
   */
  async getTenantConfigs(tenantId: number) {
    const cacheKey = `tenant:${tenantId}:configs`;

    try {
      let configs = await this.cacheManager.get(cacheKey);
      if (configs) {
        return configs;
      }

      const publicDb = this.databaseFactory.getPublicClient();
      const configRecords = await publicDb.tenantConfig.findMany({
        where: { tenantId },
      });

      // 转换为键值对格式
      configs = configRecords.reduce((acc, config) => {
        const key = `${config.category}.${config.key}`;
        acc[key] = this.parseConfigValue(config.value, config.dataType);
        return acc;
      }, {});

      // 缓存30分钟
      await this.cacheManager.set(cacheKey, configs, 1800);

      return configs;
    } catch (error) {
      this.logger.error(`获取租户配置失败: ${tenantId}`, error);
      throw error;
    }
  }

  /**
   * 清除租户缓存
   */
  async clearTenantCache(tenantId: number) {
    const keys = [
      `tenant:${tenantId}:config`,
      `tenant:${tenantId}:info`,
      `tenant:${tenantId}:features`,
      `tenant:${tenantId}:configs`,
    ];

    for (const key of keys) {
      await this.cacheManager.del(key);
    }

    this.logger.debug(`租户缓存已清除: ${tenantId}`);
  }

  /**
   * 清除所有租户缓存
   */
  async clearAllTenantCache() {
    // 注意：这个方法的实现取决于cache-manager的具体实现
    // 某些缓存提供者可能不支持模式匹配删除
    try {
      // 尝试使用store的reset方法，如果不存在则跳过
      const store = (this.cacheManager as any).store;
      if (store && typeof store.reset === 'function') {
        await store.reset();
      } else {
        this.logger.warn('缓存store不支持reset方法，跳过全局清除');
      }
    } catch (error) {
      this.logger.warn('清除全局缓存失败，跳过', error);
    }
    this.logger.debug('所有租户缓存清除操作完成');
  }

  /**
   * 从数据库获取租户完整配置
   */
  private async fetchTenantConfigFromDb(tenantId: number) {
    const publicDb = this.databaseFactory.getPublicClient();

    const tenant = await publicDb.tenant.findUnique({
      where: { id: tenantId },
      include: {
        datasource: true,
        subscriptions: {
          include: { plan: true },
          where: { status: 'active' },
          orderBy: { createdAt: 'desc' },
          take: 1,
        },
        features: {
          where: { enabled: true },
        },
        configs: true,
      },
    });

    if (!tenant) {
      throw new Error(`租户 ${tenantId} 不存在`);
    }

    return {
      tenant: {
        id: tenant.id,
        code: tenant.code,
        name: tenant.name,
        domain: tenant.domain,
        status: tenant.status,
      },
      datasource: tenant.datasource,
      currentSubscription: tenant.subscriptions[0] || null,
      enabledFeatures: tenant.features,
      configs: tenant.configs.reduce((acc, config) => {
        const key = `${config.category}.${config.key}`;
        acc[key] = this.parseConfigValue(config.value, config.dataType);
        return acc;
      }, {}),
      isSharedDatabase: tenant.datasource?.isShared ?? true,
    };
  }

  /**
   * 解析配置值
   */
  private parseConfigValue(value: string, dataType: string): any {
    try {
      switch (dataType) {
        case 'number':
          return Number(value);
        case 'boolean':
          return value === 'true';
        case 'json':
          return JSON.parse(value);
        case 'array':
          return JSON.parse(value);
        default:
          return value;
      }
    } catch {
      return value;
    }
  }
}
