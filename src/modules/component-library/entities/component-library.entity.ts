import { ApiProperty } from '@nestjs/swagger';
import { JsonValue } from '@prisma/client/runtime/library';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsBoolean, IsInt, IsObject, IsDate, IsArray } from 'class-validator';

export class ComponentLibraryEntity {
  @ApiProperty({ description: '组件ID' })
  id: number;

  @ApiProperty({ description: '组件名称' })
  @IsString()
  name: string;

  @ApiProperty({ description: '组件描述', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({
    description: '组件类型',
    examples: ['basic', 'layout', 'form', 'media', 'navigation', 'ecommerce'],
    default: 'basic',
  })
  @IsString()
  type: string;

  @ApiProperty({
    description: '组件分类',
    examples: ['header', 'footer', 'hero', 'content', 'sidebar', 'button', 'form'],
    default: 'content',
  })
  @IsString()
  category: string;

  @ApiProperty({ description: '组件标签', type: [String] })
  @IsArray()
  @IsString({ each: true })
  tags: string[];

  @ApiProperty({ description: '组件配置架构', type: 'object' })
  @IsObject()
  schema: JsonValue;

  @ApiProperty({ description: '默认属性配置', type: 'object' })
  @IsObject()
  defaultProps: JsonValue;

  @ApiProperty({ description: '样式配置', type: 'object', required: false })
  @IsOptional()
  @IsObject()
  styleConfig?: JsonValue;

  @ApiProperty({ description: '组件预览图URL', required: false })
  @IsOptional()
  @IsString()
  previewImage?: string;

  @ApiProperty({ description: '组件图标', required: false })
  @IsOptional()
  @IsString()
  icon?: string;

  @ApiProperty({ description: '是否为系统组件', default: false })
  @IsBoolean()
  isSystem: boolean;

  @ApiProperty({ description: '是否激活', default: true })
  @IsBoolean()
  isActive: boolean;

  @ApiProperty({ description: '排序权重', default: 0 })
  @IsInt()
  sortOrder: number;

  @ApiProperty({ description: '版本号', default: '1.0.0' })
  @IsString()
  version: string;

  @ApiProperty({ description: '组件元数据', type: 'object', required: false })
  @IsOptional()
  @IsObject()
  metadata?: JsonValue;

  @ApiProperty({ description: '租户ID' })
  tenantId: number;

  @ApiProperty({ description: '创建者ID' })
  userId: number;

  @ApiProperty({ description: '创建时间' })
  @IsDate()
  @Type(() => Date)
  createTime: Date;

  @ApiProperty({ description: '更新时间' })
  @IsDate()
  @Type(() => Date)
  updateTime: Date;

  // 关联对象（可选）
  @ApiProperty({ description: '创建者信息', required: false })
  @IsOptional()
  user?: any;

  @ApiProperty({ description: '使用次数', required: false })
  @IsOptional()
  @IsInt()
  usageCount?: number;
}
