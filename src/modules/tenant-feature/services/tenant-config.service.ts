import * as crypto from 'crypto';

import { CACHE_MANAGER } from '@nestjs/cache-manager';
import { ForbiddenException, Inject, Injectable, Logger, NotFoundException } from '@nestjs/common';
import { Cache } from 'cache-manager';

import { TenantFeatureService } from './tenant-feature.service';

import { DatabaseFactory } from '@/core/database/database.factory';
@Injectable()
export class TenantConfigService {
  private readonly logger = new Logger(TenantConfigService.name);
  private readonly encryptionKey: Buffer;
  private readonly encryptionIv: Buffer;

  constructor(
    private readonly prisma: PublicPrismaService,
    @Inject(CACHE_MANAGER) private readonly cacheManager: Cache,
    private readonly tenantFeatureService: TenantFeatureService,
  ) {
    // 从环境变量获取加密密钥，或使用默认值
    // 注意：在生产环境中，应该使用更安全的方式存储和管理密钥
    const key = process.env.CONFIG_ENCRYPTION_KEY || 'a-very-secret-key-for-tenant-config-123';
    this.encryptionKey = crypto.scryptSync(key, 'salt', 32);
    this.encryptionIv = Buffer.alloc(16, 0);
  }

  /**
   * 获取配置类别列表
   */
  async getCategories(): Promise<any[]> {
    const cacheKey = 'tenant-config-categories';

    // 尝试从缓存获取
    let categories = await this.cacheManager.get<any[]>(cacheKey);

    if (!categories) {
      // 定义配置类别
      categories = [
        {
          code: 'email',
          name: '邮件服务',
          description: '配置邮件发送服务',
          requiredFeature: 'tenant-config.email',
        },
        {
          code: 'sms',
          name: '短信服务',
          description: '配置短信发送服务',
          requiredFeature: 'tenant-config.sms',
        },
        {
          code: 'oss',
          name: '对象存储',
          description: '配置文件存储服务',
          requiredFeature: 'tenant-config.oss',
        },
        {
          code: 'payment',
          name: '支付服务',
          description: '配置支付处理服务',
          requiredFeature: 'tenant-config.payment',
        },
      ];

      // 存入缓存
      await this.cacheManager.set(cacheKey, categories, 3600);
    }

    return categories;
  }

  /**
   * 获取租户配置
   * @param tenantId 租户ID
   * @param category 配置类别
   * @param userType 用户类型，'SYSTEM'表示系统管理员，'TENANT'表示租户用户
   */
  async getConfig(tenantId: number, category: string, userType?: string): Promise<any> {
    // 如果不是系统管理员，检查租户是否有权限使用该类别的功能
    if (userType !== 'SYSTEM') {
      const featureCode = `tenant-config.${category}`;
      const hasFeature = await this.tenantFeatureService.hasFeature(tenantId, featureCode);

      if (!hasFeature) {
        throw new ForbiddenException(`租户无权访问 ${category} 配置`);
      }
    }

    // 构建缓存键
    const cacheKey = `tenant:${tenantId}:config:${category}`;

    // 强制清除缓存，确保从数据库获取最新数据
    await this.clearConfigCache(tenantId, category);

    // 跳过缓存，直接从数据库获取
    let config = undefined;

    // 确保 tenantId 是数字类型
    const numericTenantId = typeof tenantId === 'string' ? parseInt(tenantId, 10) : tenantId;

    this.logger.log(
      `尝试从数据库获取配置，租户ID: ${numericTenantId} (${typeof numericTenantId}), 类别: ${category}`,
    );

    // 从数据库获取配置
    let configItems = [];
    try {
      configItems = await this.prisma.tenantConfig.findMany({
        where: {
          tenantId: numericTenantId,
          category,
        },
      });
    } catch (error) {
      this.logger.error(`从数据库获取配置失败: ${error.message}`, error.stack);
      return {};
    }

    this.logger.log(
      `获取到 ${configItems.length} 条配置记录，租户ID: ${numericTenantId}, 类别: ${category}`,
    );

    if (configItems.length > 0) {
      this.logger.debug(`配置记录示例: ${JSON.stringify(configItems[0], null, 2)}`);
    }

    // 转换为对象
    config = configItems.reduce((acc, item) => {
      acc[item.key] = this.decryptConfigValue(item);
      return acc;
    }, {});

    // 存入缓存
    await this.cacheManager.set(cacheKey, config, 300);

    return config;
  }

  /**
   * 更新租户配置
   * @param tenantId 租户ID
   * @param category 配置类别
   * @param data 配置数据
   * @param userType 用户类型，'SYSTEM'表示系统管理员，'TENANT'表示租户用户
   */
  async updateConfig(
    tenantId: number,
    category: string,
    data: Record<string, any>,
    userType?: string,
  ): Promise<void> {
    // 如果不是系统管理员，检查租户是否有权限使用该类别的功能
    if (userType !== 'SYSTEM') {
      const featureCode = `tenant-config.${category}`;
      const hasFeature = await this.tenantFeatureService.hasFeature(tenantId, featureCode);

      if (!hasFeature) {
        throw new ForbiddenException(`租户无权访问 ${category} 配置`);
      }
    }

    // 获取敏感字段列表
    const sensitiveFields = this.getSensitiveFields(category);

    // 批量更新配置
    const operations = Object.entries(data).map(([key, value]) => {
      const encrypted = sensitiveFields.includes(key);
      // 确保值是字符串类型
      const stringValue = value === null || value === undefined ? '' : String(value);
      const storedValue = encrypted ? this.encryptValue(stringValue) : stringValue;

      return this.prisma.tenantConfig.upsert({
        where: {
          tenantId_category_key: {
            tenantId,
            category,
            key,
          },
        },
        update: {
          value: storedValue,
          encrypted,
        },
        create: {
          tenantId,
          category,
          key,
          value: storedValue,
          encrypted,
        },
      });
    });

    await this.prisma.$transaction(operations);

    // 清除缓存
    await this.clearConfigCache(tenantId, category);
  }

  /**
   * 测试配置
   * @param tenantId 租户ID
   * @param category 配置类别
   * @param testData 测试数据
   * @param userType 用户类型，'SYSTEM'表示系统管理员，'TENANT'表示租户用户
   */
  async testConfig(
    tenantId: number,
    category: string,
    testData: Record<string, any>,
    userType?: string,
  ): Promise<any> {
    // 如果不是系统管理员，检查租户是否有权限使用该类别的功能
    if (userType !== 'SYSTEM') {
      const featureCode = `tenant-config.${category}`;
      const hasFeature = await this.tenantFeatureService.hasFeature(tenantId, featureCode);

      if (!hasFeature) {
        throw new ForbiddenException(`租户无权访问 ${category} 配置`);
      }
    }

    // 获取配置
    const config = await this.getConfig(tenantId, category, userType);

    // 根据配置类别执行不同的测试
    switch (category) {
      case 'email':
        return this.testEmailConfig(config, testData);
      case 'sms':
        return this.testSmsConfig(config, testData);
      case 'oss':
        return this.testOssConfig(config, testData);
      case 'payment':
        return this.testPaymentConfig(config, testData);
      default:
        throw new NotFoundException(`不支持测试 ${category} 配置`);
    }
  }

  /**
   * 获取配置模板
   * @param category 配置类别
   */
  async getConfigTemplates(category: string): Promise<any[]> {
    const cacheKey = `config-templates:${category}`;

    // 尝试从缓存获取
    let templates = await this.cacheManager.get<any[]>(cacheKey);

    if (!templates) {
      // 根据配置类别返回不同的模板
      switch (category) {
        case 'email':
          templates = this.getEmailConfigTemplates();
          break;
        case 'sms':
          templates = this.getSmsConfigTemplates();
          break;
        case 'oss':
          templates = this.getOssConfigTemplates();
          break;
        case 'payment':
          templates = this.getPaymentConfigTemplates();
          break;
        default:
          templates = [];
      }

      // 存入缓存
      await this.cacheManager.set(cacheKey, templates, 3600);
    }

    return templates;
  }

  /**
   * 获取敏感字段列表
   * @param category 配置类别
   */
  private getSensitiveFields(category: string): string[] {
    switch (category) {
      case 'email':
        return ['password', 'apiKey', 'secretKey'];
      case 'sms':
        return ['accessKeySecret', 'apiKey', 'secretKey'];
      case 'oss':
        return ['accessKeyId', 'accessKeySecret', 'secretKey'];
      case 'payment':
        return ['privateKey', 'apiKey', 'secretKey', 'appSecret'];
      default:
        return [];
    }
  }

  /**
   * 加密配置值
   * @param value 配置值
   */
  private encryptValue(value: string): string {
    if (!value) return '';

    try {
      const cipher = crypto.createCipheriv('aes-256-cbc', this.encryptionKey, this.encryptionIv);
      let encrypted = cipher.update(value, 'utf8', 'hex');
      encrypted += cipher.final('hex');
      return encrypted;
    } catch (error) {
      this.logger.error(`加密配置值失败: ${error.message}`);
      return value;
    }
  }

  /**
   * 解密配置值
   * @param configItem 配置项
   */
  private decryptConfigValue(configItem: any): any {
    if (!configItem) {
      this.logger.log(`解密配置值: 配置项为空`);
      return null;
    }

    this.logger.log(
      `解密配置值: 配置项 key=${configItem.key}, value=${configItem.value}, encrypted=${configItem.encrypted}`,
    );

    if (!configItem.encrypted) {
      this.logger.log(`解密配置值: 配置项未加密，直接返回值: ${configItem.value}`);
      return configItem.value;
    }

    try {
      // 检查加密值是否为有效的十六进制字符串
      const hexRegex = /^[0-9a-fA-F]+$/;
      if (!hexRegex.test(configItem.value)) {
        this.logger.error(`解密配置值失败: 值不是有效的十六进制字符串: ${configItem.value}`);
        return configItem.value; // 如果不是有效的十六进制字符串，直接返回原值
      }

      const decipher = crypto.createDecipheriv(
        'aes-256-cbc',
        this.encryptionKey,
        this.encryptionIv,
      );
      let decrypted = decipher.update(configItem.value, 'hex', 'utf8');
      decrypted += decipher.final('utf8');
      this.logger.log(`解密配置值成功: ${configItem.value} -> ${decrypted}`);
      return decrypted;
    } catch (error) {
      this.logger.error(`解密配置值失败: ${error.message}`);
      return configItem.value; // 解密失败时返回原值，而不是掩码
    }
  }

  /**
   * 清除配置缓存
   * @param tenantId 租户ID
   * @param category 配置类别
   */
  private async clearConfigCache(tenantId: number, category: string): Promise<void> {
    await this.cacheManager.del(`tenant:${tenantId}:config:${category}`);
  }

  // 以下是各种配置模板和测试方法的实现
  // 在实际项目中，这些方法应该更加完善，并可能放在单独的服务中

  private getEmailConfigTemplates(): any[] {
    return [
      {
        name: 'SMTP',
        description: '标准SMTP服务器配置',
        template: {
          provider: 'smtp',
          host: '',
          port: 587,
          username: '',
          password: '',
          fromName: '',
          fromEmail: '',
          enableSSL: true,
        },
      },
      {
        name: '阿里云邮件',
        description: '阿里云邮件推送服务',
        template: {
          provider: 'aliyun',
          accessKeyId: '',
          accessKeySecret: '',
          fromName: '',
          fromEmail: '',
          region: 'cn-hangzhou',
        },
      },
    ];
  }

  private getSmsConfigTemplates(): any[] {
    return [
      {
        name: '阿里云短信',
        description: '阿里云短信服务',
        template: {
          provider: 'aliyun',
          accessKeyId: '',
          accessKeySecret: '',
          signName: '',
          region: 'cn-hangzhou',
        },
      },
      {
        name: '腾讯云短信',
        description: '腾讯云短信服务',
        template: {
          provider: 'tencent',
          appId: '',
          appKey: '',
          signName: '',
        },
      },
    ];
  }

  private getOssConfigTemplates(): any[] {
    return [
      {
        name: '阿里云OSS',
        description: '阿里云对象存储服务',
        template: {
          provider: 'aliyun',
          accessKeyId: '',
          accessKeySecret: '',
          bucket: '',
          region: 'oss-cn-hangzhou',
          endpoint: 'https://oss-cn-hangzhou.aliyuncs.com',
        },
      },
      {
        name: '腾讯云COS',
        description: '腾讯云对象存储服务',
        template: {
          provider: 'tencent',
          secretId: '',
          secretKey: '',
          bucket: '',
          region: 'ap-guangzhou',
        },
      },
    ];
  }

  private getPaymentConfigTemplates(): any[] {
    return [
      {
        name: '支付宝',
        description: '支付宝支付服务',
        template: {
          provider: 'alipay',
          appId: '',
          privateKey: '',
          publicKey: '',
          notifyUrl: '',
          returnUrl: '',
        },
      },
      {
        name: '微信支付',
        description: '微信支付服务',
        template: {
          provider: 'wechat',
          appId: '',
          mchId: '',
          apiKey: '',
          notifyUrl: '',
          certPath: '',
        },
      },
    ];
  }

  private async testEmailConfig(config: any, testData: any): Promise<any> {
    // 模拟邮件发送测试
    // 在实际项目中，应该实现真正的邮件发送测试
    this.logger.log(`测试邮件配置: ${JSON.stringify(config)}`);
    this.logger.log(`测试数据: ${JSON.stringify(testData)}`);

    // 返回开发中提示
    return {
      success: false,
      inDevelopment: true,
      message: '邮件测试功能正在开发中，敬请期待',
    };
  }

  private async testSmsConfig(config: any, testData: any): Promise<any> {
    // 模拟短信发送测试
    this.logger.log(`测试短信配置: ${JSON.stringify(config)}`);
    this.logger.log(`测试数据: ${JSON.stringify(testData)}`);

    // 返回开发中提示
    return {
      success: false,
      inDevelopment: true,
      message: '短信测试功能正在开发中，敬请期待',
    };
  }

  private async testOssConfig(config: any, testData: any): Promise<any> {
    // 模拟对象存储测试
    this.logger.log(`测试对象存储配置: ${JSON.stringify(config)}`);
    this.logger.log(`测试数据: ${JSON.stringify(testData)}`);

    // 返回开发中提示
    return {
      success: false,
      inDevelopment: true,
      message: '对象存储测试功能正在开发中，敬请期待',
    };
  }

  private async testPaymentConfig(config: any, testData: any): Promise<any> {
    // 模拟支付测试
    this.logger.log(`测试支付配置: ${JSON.stringify(config)}`);
    this.logger.log(`测试数据: ${JSON.stringify(testData)}`);

    // 返回开发中提示
    return {
      success: false,
      inDevelopment: true,
      message: '支付测试功能正在开发中，敬请期待',
    };
  }
}
