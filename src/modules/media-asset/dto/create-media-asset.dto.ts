import { ApiProperty } from '@nestjs/swagger';
import { JsonValue } from '@prisma/client/runtime/library';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsInt, IsObject, IsArray, Min } from 'class-validator';

export class CreateMediaAssetDto {
  @ApiProperty({ description: '文件名' })
  @IsString()
  filename: string;

  @ApiProperty({ description: '原始文件名' })
  @IsString()
  originalName: string;

  @ApiProperty({ description: 'MIME类型' })
  @IsString()
  mimeType: string;

  @ApiProperty({ description: '文件大小(字节)' })
  @IsInt()
  @Min(0)
  @Type(() => Number)
  size: number;

  @ApiProperty({ description: '文件URL' })
  @IsString()
  url: string;

  @ApiProperty({ description: '缩略图URL', required: false })
  @IsOptional()
  @IsString()
  thumbnailUrl?: string;

  @ApiProperty({ description: '替代文本', required: false })
  @IsOptional()
  @IsString()
  alt?: string;

  @ApiProperty({
    description: '媒体分类',
    examples: ['image', 'video', 'document', 'audio'],
    default: 'image',
  })
  @IsOptional()
  @IsString()
  category?: string = 'image';

  @ApiProperty({
    description: '标签数组',
    type: [String],
    required: false,
    example: ['产品图片', '横幅', '首页'],
  })
  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  tags?: string[] = [];

  @ApiProperty({
    description: '文件元数据',
    type: 'object',
    required: false,
    example: {
      width: 1920,
      height: 1080,
      exif: {},
      uploadedBy: 'admin',
      source: 'upload',
    },
  })
  @IsOptional()
  @IsObject()
  metadata?: JsonValue;

  @ApiProperty({ description: '所属网站ID', required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  websiteId?: number;
}
