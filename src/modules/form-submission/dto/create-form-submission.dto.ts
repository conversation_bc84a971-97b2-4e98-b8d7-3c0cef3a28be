import { ApiProperty } from '@nestjs/swagger';
import { JsonValue } from '@prisma/client/runtime/library';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsInt, IsObject, IsIP, Min } from 'class-validator';

export class CreateFormSubmissionDto {
  @ApiProperty({
    description: '表单数据',
    type: 'object',
    example: {
      name: '张三',
      email: '<EMAIL>',
      phone: '13800138000',
      company: '示例公司',
      message: '我对您的产品很感兴趣，希望了解更多信息。',
    },
  })
  @IsObject()
  data: JsonValue;

  @ApiProperty({ description: '提交者IP地址', required: false })
  @IsOptional()
  @IsIP()
  ipAddress?: string;

  @ApiProperty({ description: '提交者用户代理', required: false })
  @IsOptional()
  @IsString()
  userAgent?: string;

  @ApiProperty({ description: '来源页面URL', required: false })
  @IsOptional()
  @IsString()
  referrer?: string;

  @ApiProperty({
    description: '额外信息',
    type: 'object',
    required: false,
    example: {
      source: 'contact-form',
      campaign: 'spring-promotion',
      utm_source: 'google',
      utm_medium: 'cpc',
      utm_campaign: 'brand',
    },
  })
  @IsOptional()
  @IsObject()
  metadata?: JsonValue;

  @ApiProperty({ description: '所属表单ID' })
  @IsInt()
  @Min(1)
  @Type(() => Number)
  formId: number;
}
