import { Injectable, Logger, Inject, BadRequestException } from '@nestjs/common';

import { DepartmentStrategyType } from './department-strategy.factory';
import { IDepartmentStrategy } from './department-strategy.interface';
import { CreateDepartmentDto } from '../dto/create-department.dto';
import { DepartmentDto, DepartmentTreeDto, DepartmentListItemDto } from '../dto/department.dto';
import { QueryDepartmentDto } from '../dto/query-department.dto';
import { UpdateDepartmentStatusDto } from '../dto/update-department-status.dto';
import { UpdateDepartmentDto } from '../dto/update-department.dto';

import { ApiCode, ApiMessage } from '@/core/common/constants/api-code.constant';
import { ResourceExistsException } from '@/core/common/exceptions/resource-exists.exception';
import { ResourceNotFoundException } from '@/core/common/exceptions/resource-not-found.exception';
import { ValidationException } from '@/core/common/exceptions/validation.exception';
import { DateFormatUtil } from '@/core/common/utils/date-format.util';
import { DatabaseFactory } from '@/core/database/database.factory';
/**
 * 租户部门策略
 * 处理租户部门相关操作
 */
@Injectable()
export class TenantDepartmentStrategy implements IDepartmentStrategy {
  private readonly logger = new Logger(TenantDepartmentStrategy.name);

  constructor(@Inject(TENANT_PRISMA_SERVICE) private readonly prisma: any) {
    this.logger.log('TenantDepartmentStrategy 已创建');
    // 注意：prisma 可能为 null，因为它是请求作用域的
    this.logger.log(`TENANT_PRISMA_SERVICE 已注入: ${!!this.prisma}`);
  }

  /**
   * 获取策略类型
   * @returns 策略类型
   */
  getType(): string {
    return DepartmentStrategyType.TENANT;
  }

  /**
   * 创建租户部门
   * @param createDepartmentDto 创建部门数据
   * @param tenantId 租户ID
   * @returns 创建的部门
   */
  async create(createDepartmentDto: CreateDepartmentDto, tenantId: string): Promise<DepartmentDto> {
    try {
      if (!tenantId) {
        throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
      }

      // 检查prisma是否可用
      if (!this.prisma) {
        throw new BadRequestException('租户数据库连接不可用');
      }

      // 验证父部门是否存在
      if (createDepartmentDto.pid > 0) {
        const parentExists = await this.prisma.department.findFirst({
          where: {
            id: createDepartmentDto.pid,
            tenantId,
          },
        });

        if (!parentExists) {
          throw new ResourceNotFoundException('父部门', createDepartmentDto.pid);
        }
      }

      // 验证部门名称在同一父部门下是否已存在
      const nameExists = await this.prisma.department.findFirst({
        where: {
          name: createDepartmentDto.name,
          pid: createDepartmentDto.pid,
          tenantId,
        },
      });

      if (nameExists) {
        throw new ResourceExistsException('部门', '名称', createDepartmentDto.name);
      }

      // 如果pid为0，设置为null（表示顶级部门）
      const pid = createDepartmentDto.pid === 0 ? null : createDepartmentDto.pid;

      // 创建部门
      const department = await this.prisma.department.create({
        data: {
          name: createDepartmentDto.name,
          pid: pid,
          status: createDepartmentDto.status ?? 1,
          orderNo: createDepartmentDto.orderNo ?? 0,
          remark: createDepartmentDto.remark,
          tenantId,
        },
      });

      // 返回时将pid为null的部门转换为pid为0的部门
      return {
        id: department.id,
        name: department.name,
        pid: department.pid === null ? 0 : department.pid,
        status: department.status,
        orderNo: department.orderNo,
        createTime: DateFormatUtil.formatToDateTime(department.createdAt),
        updateTime: DateFormatUtil.formatToDateTime(department.updatedAt),
        remark: department.remark,
      };
    } catch (error) {
      this.logger.error(`创建租户部门失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取租户部门树形列表
   * @param query 查询条件
   * @param tenantId 租户ID
   * @returns 部门树形列表
   */
  async findTree(query: QueryDepartmentDto, tenantId: string): Promise<DepartmentTreeDto[]> {
    try {
      if (!tenantId) {
        throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
      }

      // 检查prisma是否可用
      if (!this.prisma) {
        throw new BadRequestException('租户数据库连接不可用');
      }

      // 构建查询条件
      const where: any = { tenantId };

      if (query.name) {
        where.name = { contains: query.name };
      }

      if (query.status !== undefined) {
        where.status = query.status;
      }

      // 查询所有部门
      const departments = await this.prisma.department.findMany({
        where,
        orderBy: [{ pid: 'asc' }, { orderNo: 'asc' }, { createdAt: 'asc' }],
      });

      // 转换为DTO，将pid为null的部门转换为pid为0的部门
      const deptDtos = departments.map(dept => ({
        id: dept.id,
        name: dept.name,
        pid: dept.pid === null ? 0 : dept.pid,
        status: dept.status,
        orderNo: dept.orderNo,
        createTime: DateFormatUtil.formatToDateTime(dept.createdAt),
        updateTime: DateFormatUtil.formatToDateTime(dept.updatedAt),
        remark: dept.remark,
        children: [],
      }));

      // 构建树形结构
      return this.buildDepartmentTree(deptDtos);
    } catch (error) {
      this.logger.error(`获取租户部门树形列表失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取租户部门列表（扁平结构）
   * @param query 查询条件
   * @param tenantId 租户ID
   * @returns 部门列表
   */
  async findList(query: QueryDepartmentDto, tenantId: string): Promise<DepartmentListItemDto[]> {
    try {
      if (!tenantId) {
        throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
      }

      // 检查prisma是否可用
      if (!this.prisma) {
        throw new BadRequestException('租户数据库连接不可用');
      }

      // 构建查询条件
      const where: any = { tenantId };

      if (query.status !== undefined) {
        where.status = query.status;
      }

      // 查询所有部门
      const departments = await this.prisma.department.findMany({
        where,
        orderBy: [{ pid: 'asc' }, { orderNo: 'asc' }, { createdAt: 'asc' }],
      });

      // 转换为DTO，将pid为null的部门转换为pid为0的部门
      const deptDtos = departments.map(dept => ({
        id: dept.id,
        name: dept.name,
        pid: dept.pid === null ? 0 : dept.pid,
        status: dept.status,
        orderNo: dept.orderNo,
        createTime: DateFormatUtil.formatToDateTime(dept.createdAt),
        updateTime: DateFormatUtil.formatToDateTime(dept.updatedAt),
        remark: dept.remark,
        children: [],
      }));

      // 构建树形结构
      return this.buildDepartmentTree(deptDtos);
    } catch (error) {
      this.logger.error(`获取租户部门列表失败: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 获取租户部门详情
   * @param id 部门ID
   * @param tenantId 租户ID
   * @returns 部门详情
   */
  async findOne(id: number, tenantId: string): Promise<DepartmentDto> {
    try {
      if (!tenantId) {
        throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
      }

      // 检查prisma是否可用
      if (!this.prisma) {
        throw new BadRequestException('租户数据库连接不可用');
      }

      const department = await this.prisma.department.findFirst({
        where: {
          id,
          tenantId,
        },
      });

      if (!department) {
        throw new ResourceNotFoundException('部门', id);
      }

      // 返回时将pid为null的部门转换为pid为0的部门
      return {
        id: department.id,
        name: department.name,
        pid: department.pid === null ? 0 : department.pid,
        status: department.status,
        orderNo: department.orderNo,
        createTime: DateFormatUtil.formatToDateTime(department.createdAt),
        updateTime: DateFormatUtil.formatToDateTime(department.updatedAt),
        remark: department.remark,
      };
    } catch (error) {
      this.logger.error(`获取租户部门详情失败，ID: ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 更新租户部门
   * @param id 部门ID
   * @param updateDepartmentDto 更新部门数据
   * @param tenantId 租户ID
   * @returns 更新后的部门
   */
  async update(
    id: number,
    updateDepartmentDto: UpdateDepartmentDto,
    tenantId: string,
  ): Promise<DepartmentDto> {
    try {
      if (!tenantId) {
        throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
      }

      // 检查prisma是否可用
      if (!this.prisma) {
        throw new BadRequestException('租户数据库连接不可用');
      }

      // 检查部门是否存在
      const department = await this.prisma.department.findFirst({
        where: {
          id,
          tenantId,
        },
      });

      if (!department) {
        throw new ResourceNotFoundException('部门', id);
      }

      // 如果pid为0，设置为null（表示顶级部门）
      if (updateDepartmentDto.pid === 0) {
        updateDepartmentDto.pid = null;
      }

      // 验证父部门是否存在
      if (updateDepartmentDto.pid !== null) {
        const parentExists = await this.prisma.department.findFirst({
          where: {
            id: updateDepartmentDto.pid,
            tenantId,
          },
        });

        if (!parentExists) {
          throw new ResourceNotFoundException('父部门', updateDepartmentDto.pid);
        }

        // 不能将部门的父部门设置为自己或其子部门
        if (updateDepartmentDto.pid === id) {
          throw new ValidationException('不能将部门的父部门设置为自己');
        }

        // 检查是否形成循环依赖
        const childDepts = await this.findChildDepartments(id, tenantId);
        if (childDepts.some(dept => dept.id === updateDepartmentDto.pid)) {
          throw new ValidationException('不能将部门的父部门设置为其子部门');
        }
      }

      // 验证部门名称在同一父部门下是否已存在（排除自己）
      const nameExists = await this.prisma.department.findFirst({
        where: {
          name: updateDepartmentDto.name,
          pid: updateDepartmentDto.pid,
          tenantId,
          id: { not: id },
        },
      });

      if (nameExists) {
        throw new ResourceExistsException('部门', '名称', updateDepartmentDto.name);
      }

      // 更新部门
      const updatedDepartment = await this.prisma.department.update({
        where: { id },
        data: {
          name: updateDepartmentDto.name,
          pid: updateDepartmentDto.pid,
          status: updateDepartmentDto.status,
          orderNo: updateDepartmentDto.orderNo,
          remark: updateDepartmentDto.remark,
        },
      });

      // 返回时将pid为null的部门转换为pid为0的部门
      return {
        id: updatedDepartment.id,
        name: updatedDepartment.name,
        pid: updatedDepartment.pid === null ? 0 : updatedDepartment.pid,
        status: updatedDepartment.status,
        orderNo: updatedDepartment.orderNo,
        createTime: DateFormatUtil.formatToDateTime(updatedDepartment.createdAt),
        updateTime: DateFormatUtil.formatToDateTime(updatedDepartment.updatedAt),
        remark: updatedDepartment.remark,
      };
    } catch (error) {
      this.logger.error(`更新租户部门失败，ID: ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 更新租户部门状态
   * @param id 部门ID
   * @param updateStatusDto 更新状态数据
   * @param tenantId 租户ID
   * @returns 更新结果
   */
  async updateStatus(
    id: number,
    updateStatusDto: UpdateDepartmentStatusDto,
    tenantId: string,
  ): Promise<{ id: number; status: number }> {
    try {
      if (!tenantId) {
        throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
      }

      // 检查prisma是否可用
      if (!this.prisma) {
        throw new BadRequestException('租户数据库连接不可用');
      }

      // 检查部门是否存在
      const department = await this.prisma.department.findFirst({
        where: {
          id,
          tenantId,
        },
      });

      if (!department) {
        throw new ResourceNotFoundException('部门', id);
      }

      // 更新部门状态
      const updatedDepartment = await this.prisma.department.update({
        where: { id },
        data: {
          status: updateStatusDto.status,
        },
        select: {
          id: true,
          status: true,
        },
      });

      return updatedDepartment;
    } catch (error) {
      this.logger.error(`更新租户部门状态失败，ID: ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 删除租户部门
   * @param id 部门ID
   * @param tenantId 租户ID
   * @returns 删除结果
   */
  async remove(id: number, tenantId: string): Promise<{ success: boolean }> {
    try {
      if (!tenantId) {
        throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
      }

      // 检查prisma是否可用
      if (!this.prisma) {
        throw new BadRequestException('租户数据库连接不可用');
      }

      // 检查部门是否存在
      const department = await this.prisma.department.findFirst({
        where: {
          id,
          tenantId,
        },
      });

      if (!department) {
        throw new ResourceNotFoundException('部门', id);
      }

      // 检查是否有子部门
      const hasChildren = await this.prisma.department.findFirst({
        where: {
          pid: id,
          tenantId,
        },
      });

      if (hasChildren) {
        throw new ValidationException('该部门下有子部门，不能删除');
      }

      // 删除部门
      await this.prisma.department.delete({
        where: { id },
      });

      return { success: true };
    } catch (error) {
      this.logger.error(`删除租户部门失败，ID: ${id}: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 检查租户部门名称是否存在
   * @param name 部门名称
   * @param pid 父部门ID
   * @param tenantId 租户ID
   * @param excludeId 排除的部门ID（可选）
   * @returns 是否存在
   */
  async checkNameExists(
    name: string,
    pid: number,
    tenantId: string,
    excludeId?: number,
  ): Promise<boolean> {
    try {
      if (!tenantId) {
        throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
      }

      // 检查prisma是否可用
      if (!this.prisma) {
        throw new BadRequestException('租户数据库连接不可用');
      }

      // 如果pid为0，设置为null（表示顶级部门）
      const actualPid = pid === 0 ? null : pid;

      const where: any = {
        name,
        pid: actualPid,
        tenantId,
      };

      if (excludeId) {
        where.id = { not: excludeId };
      }

      const department = await this.prisma.department.findFirst({
        where,
      });

      return !!department;
    } catch (error) {
      this.logger.error(
        `检查租户部门名称是否存在失败，名称: ${name}: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * 构建部门树形结构
   * @param departments 部门列表
   * @returns 树形结构
   */
  private buildDepartmentTree(departments: DepartmentTreeDto[]): DepartmentTreeDto[] {
    // 创建一个映射表，用于快速查找部门
    const deptMap = new Map<number, DepartmentTreeDto>();
    departments.forEach(dept => deptMap.set(dept.id, dept));

    // 构建树形结构
    const tree: DepartmentTreeDto[] = [];

    departments.forEach(dept => {
      // 如果是顶级部门（pid为0）或者找不到父部门，直接添加到树中
      if (dept.pid === 0 || !deptMap.has(dept.pid)) {
        tree.push(dept);
      } else {
        // 否则，添加到父部门的children中
        const parent = deptMap.get(dept.pid);
        if (parent) {
          parent.children.push(dept);
        } else {
          // 如果找不到父部门，作为顶级节点
          tree.push(dept);
        }
      }
    });

    return tree;
  }

  /**
   * 查找部门的所有子部门
   * @param departmentId 部门ID
   * @param tenantId 租户ID
   * @returns 子部门列表
   */
  private async findChildDepartments(
    departmentId: number,
    tenantId: string,
  ): Promise<{ id: number }[]> {
    const children = await this.prisma.department.findMany({
      where: {
        pid: departmentId,
        tenantId,
      },
      select: {
        id: true,
      },
    });

    const result = [...children];

    // 递归查找子部门的子部门
    for (const child of children) {
      const grandChildren = await this.findChildDepartments(child.id, tenantId);
      result.push(...grandChildren);
    }

    return result;
  }
}
