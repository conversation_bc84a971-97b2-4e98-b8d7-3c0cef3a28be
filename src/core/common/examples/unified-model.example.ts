/**
 * 统一数据模型示例
 * 展示如何使用基础类、工具类和常量来统一数据模型
 */

import { ApiProperty } from '@nestjs/swagger';

import { BaseQueryDto, BaseListResponseDto } from '../base/base-dto';
import { BaseTenantEntity } from '../base/base-entity';
import { WebsiteStatus, WEBSITE_DEFAULTS } from '../constants/model.constant';
import { DataMapperUtil } from '../utils/data-mapper.util';

/**
 * 示例：网站实体（使用基础实体类）
 */
export class WebsiteEntity extends BaseTenantEntity {
  @ApiProperty({ description: '网站名称' })
  name: string;

  @ApiProperty({ description: '网站域名' })
  domain?: string;

  @ApiProperty({ description: '网站状态', enum: WebsiteStatus })
  status: WebsiteStatus;

  @ApiProperty({ description: '网站配置', type: 'object' })
  config: any;

  /**
   * 从数据库模型映射
   */
  static fromDbModel(dbWebsite: any): WebsiteEntity {
    const baseFields = DataMapperUtil.mapTenantEntity(dbWebsite);

    return {
      ...baseFields,
      name: dbWebsite.name,
      domain: dbWebsite.domain,
      status: dbWebsite.status || WEBSITE_DEFAULTS.STATUS,
      config: DataMapperUtil.mapJsonField(dbWebsite.config),
    };
  }
}

/**
 * 示例：网站查询DTO（使用基础查询类）
 */
export class WebsiteQueryDto extends BaseQueryDto {
  @ApiProperty({ description: '网站状态过滤', enum: WebsiteStatus, required: false })
  status?: WebsiteStatus;
}

/**
 * 示例：网站列表响应（使用基础列表响应类）
 */
export class WebsiteListResponseDto extends BaseListResponseDto<WebsiteEntity> {
  @ApiProperty({ description: '网站列表', type: [WebsiteEntity] })
  items: WebsiteEntity[];

  static create(
    websites: any[],
    total: number,
    page: number,
    limit: number,
  ): WebsiteListResponseDto {
    const items = websites.map(w => WebsiteEntity.fromDbModel(w));
    return {
      ...DataMapperUtil.buildPagedResponse(items, total, page, limit),
      items,
    };
  }
}

/**
 * 示例：服务层数据映射
 */
export class WebsiteService {
  /**
   * 统一的分页查询处理
   */
  async findMany(query: WebsiteQueryDto): Promise<WebsiteListResponseDto> {
    // 模拟数据库查询
    const dbResults = [
      {
        id: 1,
        tenantId: 1,
        name: '示例网站',
        domain: 'example.com',
        status: 'published',
        config: { theme: 'default' },
        createdAt: new Date(),
        updatedAt: new Date(),
      },
    ];

    const total = 1;

    // 使用统一的响应构建方法
    return WebsiteListResponseDto.create(dbResults, total, query.page || 1, query.limit || 10);
  }

  /**
   * 统一的单个记录处理
   */
  async findById(id: number): Promise<WebsiteEntity> {
    // 模拟数据库查询
    const dbResult = {
      id,
      tenantId: 1,
      name: '示例网站',
      domain: 'example.com',
      status: 'published',
      config: { theme: 'default' },
      createdAt: new Date(),
      updatedAt: new Date(),
    };

    // 使用统一的实体映射方法
    return WebsiteEntity.fromDbModel(dbResult);
  }
}
