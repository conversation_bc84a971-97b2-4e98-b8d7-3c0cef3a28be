import { CurrentTenant } from '@core/decorators/current-tenant.decorator';
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiParam,
  ApiQuery,
  ApiBearerAuth,
} from '@nestjs/swagger';

import { CreateWebsiteFormDto } from './dto/create-website-form.dto';
import { QueryWebsiteFormDto } from './dto/query-website-form.dto';
import { UpdateWebsiteFormDto } from './dto/update-website-form.dto';
import { WebsiteFormEntity } from './entities/website-form.entity';
import { WebsiteFormService } from './website-form.service';

@ApiTags('网站表单管理')
@ApiBearerAuth()
@Controller('website-forms')
export class WebsiteFormController {
  constructor(private readonly websiteFormService: WebsiteFormService) {}

  @Post()
  @ApiOperation({ summary: '创建网站表单' })
  @ApiResponse({
    status: 201,
    description: '表单创建成功',
    type: WebsiteFormEntity,
  })
  @ApiResponse({ status: 409, description: '表单名称已存在' })
  @ApiResponse({ status: 404, description: '网站不存在' })
  async create(
    @CurrentTenant('id') tenantId: number,
    @Body() createDto: CreateWebsiteFormDto,
  ): Promise<WebsiteFormEntity> {
    return this.websiteFormService.create(tenantId, createDto);
  }

  @Get()
  @ApiOperation({ summary: '获取网站表单列表' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        data: { type: 'array', items: { $ref: '#/components/schemas/WebsiteFormEntity' } },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
        totalPages: { type: 'number' },
      },
    },
  })
  async findAll(@CurrentTenant('id') tenantId: number, @Query() queryDto: QueryWebsiteFormDto) {
    return this.websiteFormService.findAll(tenantId, queryDto);
  }

  @Get('stats')
  @ApiOperation({ summary: '获取表单统计信息' })
  @ApiQuery({ name: 'formId', required: false, description: '表单ID' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        totalSubmissions: { type: 'number' },
        recentSubmissions: { type: 'number' },
        statusDistribution: { type: 'object' },
      },
    },
  })
  async getStats(
    @CurrentTenant('id') tenantId: number,
    @Query('formId', ParseIntPipe) formId?: number,
  ) {
    return this.websiteFormService.getFormStats(tenantId, formId);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取单个网站表单详情' })
  @ApiParam({ name: 'id', description: '表单ID' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: WebsiteFormEntity,
  })
  @ApiResponse({ status: 404, description: '表单不存在' })
  async findOne(
    @CurrentTenant('id') tenantId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<WebsiteFormEntity> {
    return this.websiteFormService.findOne(tenantId, id);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新网站表单' })
  @ApiParam({ name: 'id', description: '表单ID' })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    type: WebsiteFormEntity,
  })
  @ApiResponse({ status: 404, description: '表单不存在' })
  @ApiResponse({ status: 409, description: '表单名称已存在' })
  async update(
    @CurrentTenant('id') tenantId: number,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateWebsiteFormDto,
  ): Promise<WebsiteFormEntity> {
    return this.websiteFormService.update(tenantId, id, updateDto);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除网站表单' })
  @ApiParam({ name: 'id', description: '表单ID' })
  @ApiResponse({
    status: 200,
    description: '删除成功',
  })
  @ApiResponse({ status: 404, description: '表单不存在' })
  @ApiResponse({ status: 409, description: '表单包含提交记录，无法删除' })
  async remove(
    @CurrentTenant('id') tenantId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<void> {
    return this.websiteFormService.remove(tenantId, id);
  }

  @Post(':id/toggle-active')
  @ApiOperation({ summary: '切换表单激活状态' })
  @ApiParam({ name: 'id', description: '表单ID' })
  @ApiResponse({
    status: 200,
    description: '操作成功',
    type: WebsiteFormEntity,
  })
  @ApiResponse({ status: 404, description: '表单不存在' })
  async toggleActive(
    @CurrentTenant('id') tenantId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<WebsiteFormEntity> {
    return this.websiteFormService.toggleActive(tenantId, id);
  }

  @Post(':id/duplicate')
  @ApiOperation({ summary: '复制表单' })
  @ApiParam({ name: 'id', description: '表单ID' })
  @ApiResponse({
    status: 201,
    description: '复制成功',
    type: WebsiteFormEntity,
  })
  @ApiResponse({ status: 404, description: '表单不存在' })
  @ApiResponse({ status: 409, description: '新表单名称已存在' })
  async duplicate(
    @CurrentTenant('id') tenantId: number,
    @Param('id', ParseIntPipe) id: number,
    @Body() body: { name: string },
  ): Promise<WebsiteFormEntity> {
    return this.websiteFormService.duplicateForm(tenantId, id, body.name);
  }
}
