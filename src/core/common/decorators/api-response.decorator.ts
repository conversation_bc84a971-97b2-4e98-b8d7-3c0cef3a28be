import { applyDecorators, Type } from '@nestjs/common';
import { ApiResponse, ApiResponseOptions, getSchemaPath } from '@nestjs/swagger';

import {
  ApiResponseDto,
  SuccessResponseDto,
  PaginationResponseDto,
  ErrorResponseDto,
  ListResponseDto,
} from '../dto/api-response.dto';

/**
 * API成功响应装饰器
 * @param type 数据类型
 * @param description 描述
 */
export const ApiSuccessResponse = <TModel extends Type<any>>(
  type?: TModel,
  description = '操作成功',
) => {
  const responseSchema = type
    ? {
        allOf: [
          { $ref: getSchemaPath(SuccessResponseDto) },
          {
            properties: {
              data: { $ref: getSchemaPath(type) },
            },
          },
        ],
      }
    : { $ref: getSchemaPath(SuccessResponseDto) };

  return applyDecorators(
    ApiResponse({
      status: 200,
      description,
      schema: responseSchema,
    }),
  );
};

/**
 * API分页响应装饰器
 * @param type 数据类型
 * @param description 描述
 */
export const ApiPaginationResponse = <TModel extends Type<any>>(
  type: TModel,
  description = '获取列表成功',
) => {
  return applyDecorators(
    ApiResponse({
      status: 200,
      description,
      schema: {
        allOf: [
          { $ref: getSchemaPath(PaginationResponseDto) },
          {
            properties: {
              data: {
                type: 'object',
                properties: {
                  items: {
                    type: 'array',
                    items: { $ref: getSchemaPath(type) },
                  },
                  total: { type: 'number', example: 100 },
                  page: { type: 'number', example: 1 },
                  pageSize: { type: 'number', example: 10 },
                  totalPages: { type: 'number', example: 10 },
                },
              },
            },
          },
        ],
      },
    }),
  );
};

/**
 * API列表响应装饰器
 * @param type 数据类型
 * @param description 描述
 */
export const ApiListResponse = <TModel extends Type<any>>(
  type: TModel,
  description = '获取列表成功',
) => {
  return applyDecorators(
    ApiResponse({
      status: 200,
      description,
      schema: {
        allOf: [
          { $ref: getSchemaPath(ListResponseDto) },
          {
            properties: {
              data: {
                type: 'array',
                items: { $ref: getSchemaPath(type) },
              },
            },
          },
        ],
      },
    }),
  );
};

/**
 * API错误响应装饰器
 */
export const ApiErrorResponses = () => {
  return applyDecorators(
    ApiResponse({
      status: 400,
      description: '请求参数错误',
      schema: { $ref: getSchemaPath(ErrorResponseDto) },
    }),
    ApiResponse({
      status: 401,
      description: '未授权访问',
      schema: { $ref: getSchemaPath(ErrorResponseDto) },
    }),
    ApiResponse({
      status: 403,
      description: '禁止访问',
      schema: { $ref: getSchemaPath(ErrorResponseDto) },
    }),
    ApiResponse({
      status: 404,
      description: '资源不存在',
      schema: { $ref: getSchemaPath(ErrorResponseDto) },
    }),
    ApiResponse({
      status: 500,
      description: '服务器内部错误',
      schema: { $ref: getSchemaPath(ErrorResponseDto) },
    }),
  );
};

/**
 * 完整的API响应装饰器（包含成功和错误响应）
 * @param type 成功响应的数据类型
 * @param description 成功响应描述
 */
export const ApiCompleteResponse = <TModel extends Type<any>>(
  type?: TModel,
  description = '操作成功',
) => {
  return applyDecorators(ApiSuccessResponse(type, description), ApiErrorResponses());
};

/**
 * 完整的分页响应装饰器
 * @param type 数据类型
 * @param description 描述
 */
export const ApiCompletePaginationResponse = <TModel extends Type<any>>(
  type: TModel,
  description = '获取列表成功',
) => {
  return applyDecorators(ApiPaginationResponse(type, description), ApiErrorResponses());
};
