import { ApiProperty, ApiPropertyOptional } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsEnum, IsObject, IsDate, IsInt, Min, Max } from 'class-validator';

import { BaseResponseDto, BaseListResponseDto } from '@/core/common/base/base-dto';
import { CommonStatus, NUMERIC_LIMITS } from '@/core/common/constants/model.constant';
import { DataMapperUtil } from '@/core/common/utils/data-mapper.util';

/**
 * 数据源响应DTO
 */
export class DatasourceResponseDto {
  @ApiProperty({ description: '数据源ID' })
  id: number;

  @ApiProperty({ description: '数据源名称' })
  name: string;

  @ApiProperty({ description: '数据源URL' })
  url: string;

  @ApiProperty({ description: '数据源类型' })
  type: string;

  @ApiProperty({ description: '连接状态' })
  status: string;

  @ApiProperty({ description: '数据源元数据', type: 'object' })
  metadata: any;
}

/**
 * 租户响应DTO
 */
export class TenantResponseDto extends BaseResponseDto {
  @ApiProperty({ description: '租户名称' })
  name: string;

  @ApiProperty({ description: '租户代码' })
  code: string;

  @ApiPropertyOptional({ description: '租户域名' })
  domain?: string;

  @ApiPropertyOptional({ description: '联系邮箱' })
  email?: string;

  @ApiPropertyOptional({ description: '联系电话' })
  phone?: string;

  @ApiProperty({
    description: '租户状态',
    enum: CommonStatus,
    enumName: 'CommonStatus',
  })
  @IsEnum(CommonStatus)
  status: CommonStatus;

  @ApiProperty({ description: '部署模式' })
  deploymentMode: string;

  @ApiPropertyOptional({ description: '数据源配置' })
  datasourceConfig?: DatasourceResponseDto;

  @ApiPropertyOptional({ description: '订阅计划名称' })
  subscriptionPlanName?: string;

  @ApiPropertyOptional({ description: '订阅状态' })
  subscriptionStatus?: string;

  @ApiPropertyOptional({ description: '订阅到期时间' })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  subscriptionEndDate?: Date;

  @ApiProperty({
    description: '排序权重',
    minimum: NUMERIC_LIMITS.SORT_ORDER_MIN,
    maximum: NUMERIC_LIMITS.SORT_ORDER_MAX,
  })
  @IsInt()
  @Min(NUMERIC_LIMITS.SORT_ORDER_MIN)
  @Max(NUMERIC_LIMITS.SORT_ORDER_MAX)
  sortOrder: number;

  @ApiPropertyOptional({ description: '元数据', type: 'object' })
  @IsOptional()
  @IsObject()
  metadata?: any;

  /**
   * 从数据库模型映射为响应DTO
   */
  static fromDbModel(dbTenant: any): TenantResponseDto {
    const mapped = DataMapperUtil.mapBaseEntity(dbTenant);

    return {
      ...mapped,
      name: dbTenant.name,
      code: dbTenant.code,
      domain: dbTenant.domain,
      email: dbTenant.email,
      phone: dbTenant.phone,
      status: DataMapperUtil.mapEnumField(
        dbTenant.status,
        Object.values(CommonStatus),
        CommonStatus.ENABLED,
      ),
      deploymentMode: dbTenant.deploymentMode || 'shared',
      datasourceConfig: dbTenant.datasource
        ? {
            id: dbTenant.datasource.id,
            name: dbTenant.datasource.name,
            url: dbTenant.datasource.url,
            type: dbTenant.datasource.type,
            status: dbTenant.datasource.status,
            metadata: DataMapperUtil.mapJsonField(dbTenant.datasource.metadata),
          }
        : undefined,
      subscriptionPlanName: DataMapperUtil.safeGet(dbTenant, 'activeSubscription.plan.name'),
      subscriptionStatus: DataMapperUtil.safeGet(dbTenant, 'activeSubscription.status'),
      subscriptionEndDate: DataMapperUtil.safeGet(dbTenant, 'activeSubscription.endDate'),
      sortOrder: dbTenant.sortOrder || 0,
      metadata: DataMapperUtil.mapJsonField(dbTenant.metadata),
    };
  }
}

/**
 * 租户统计响应DTO
 */
export class TenantStatsResponseDto {
  @ApiProperty({ description: '租户总数' })
  totalTenants: number;

  @ApiProperty({ description: '活跃租户数' })
  activeTenants: number;

  @ApiProperty({ description: '本月新增租户数' })
  newTenantsThisMonth: number;

  @ApiProperty({ description: '按状态分组统计', type: 'object' })
  statusBreakdown: Record<string, number>;

  @ApiProperty({ description: '按部署模式分组统计', type: 'object' })
  deploymentModeBreakdown: Record<string, number>;

  @ApiProperty({ description: '按订阅计划分组统计', type: 'object' })
  planBreakdown: Record<string, number>;

  /**
   * 从统计数据映射为响应DTO
   */
  static fromStatsData(stats: any): TenantStatsResponseDto {
    return {
      totalTenants: stats.total || 0,
      activeTenants: stats.active || 0,
      newTenantsThisMonth: stats.newThisMonth || 0,
      statusBreakdown: stats.byStatus || {},
      deploymentModeBreakdown: stats.byDeploymentMode || {},
      planBreakdown: stats.byPlan || {},
    };
  }
}

/**
 * 租户列表响应DTO
 */
export class TenantListResponseDto extends BaseListResponseDto<TenantResponseDto> {
  @ApiProperty({ description: '租户列表', type: [TenantResponseDto] })
  items: TenantResponseDto[];

  /**
   * 创建分页响应
   */
  static create(tenants: any[], total: number, page: number, limit: number): TenantListResponseDto {
    const items = tenants.map(tenant => TenantResponseDto.fromDbModel(tenant));
    const pagedResponse = DataMapperUtil.buildPagedResponse(items, total, page, limit);

    return {
      ...pagedResponse,
      items,
    };
  }
}
