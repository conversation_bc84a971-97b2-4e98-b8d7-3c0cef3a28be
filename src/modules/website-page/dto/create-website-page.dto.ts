import { ApiProperty } from '@nestjs/swagger';
import { JsonValue } from '@prisma/client/runtime/library';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsBoolean, IsInt, IsObject, Min } from 'class-validator';

export class CreateWebsitePageDto {
  @ApiProperty({ description: '页面标题' })
  @IsString()
  title: string;

  @ApiProperty({ description: '页面路径/别名' })
  @IsString()
  slug: string;

  @ApiProperty({
    description: '页面内容/组件配置',
    type: 'object',
    example: {
      components: [
        {
          id: 'header-1',
          type: 'header',
          props: {
            title: '欢迎来到我们的网站',
            subtitle: '提供优质服务',
            backgroundImage: '/images/hero-bg.jpg',
          },
          style: {
            backgroundColor: '#f8f9fa',
            padding: '60px 0',
          },
        },
        {
          id: 'content-1',
          type: 'text',
          props: {
            content: '<p>这里是页面内容...</p>',
          },
        },
      ],
      layout: 'standard',
      version: '1.0',
    },
  })
  @IsObject()
  content: JsonValue;

  @ApiProperty({
    description: '页面类型',
    examples: ['page', 'home', 'about', 'contact', 'blog'],
    default: 'page',
  })
  @IsOptional()
  @IsString()
  type?: string = 'page';

  @ApiProperty({ description: 'SEO标题', required: false })
  @IsOptional()
  @IsString()
  seoTitle?: string;

  @ApiProperty({ description: 'SEO描述', required: false })
  @IsOptional()
  @IsString()
  seoDescription?: string;

  @ApiProperty({ description: 'SEO关键词', required: false })
  @IsOptional()
  @IsString()
  seoKeywords?: string;

  @ApiProperty({ description: '自定义CSS', required: false })
  @IsOptional()
  @IsString()
  customCSS?: string;

  @ApiProperty({ description: '自定义JavaScript', required: false })
  @IsOptional()
  @IsString()
  customJS?: string;

  @ApiProperty({ description: '排序权重', default: 0 })
  @IsOptional()
  @IsInt()
  @Min(0)
  @Type(() => Number)
  sortOrder?: number = 0;

  @ApiProperty({ description: '是否为首页', default: false })
  @IsOptional()
  @IsBoolean()
  @Type(() => Boolean)
  isHomePage?: boolean = false;

  @ApiProperty({
    description: '元数据',
    type: 'object',
    required: false,
    example: {
      author: '页面作者',
      lastModified: '2024-01-01',
      tags: ['首页', '介绍'],
      customFields: {},
    },
  })
  @IsOptional()
  @IsObject()
  metadata?: JsonValue;

  @ApiProperty({ description: '所属网站ID' })
  @IsInt()
  @Min(1)
  @Type(() => Number)
  websiteId: number;
}
