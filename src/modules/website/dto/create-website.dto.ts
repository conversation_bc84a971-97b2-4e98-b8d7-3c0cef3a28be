import { ApiProperty } from '@nestjs/swagger';
import { JsonValue } from '@prisma/client/runtime/library';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsInt, IsObject, Min } from 'class-validator';

export class CreateWebsiteDto {
  @ApiProperty({ description: '网站名称' })
  @IsString()
  name: string;

  @ApiProperty({ description: '网站描述', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '自定义域名', required: false })
  @IsOptional()
  @IsString()
  domain?: string;

  @ApiProperty({ description: '子域名', required: false })
  @IsOptional()
  @IsString()
  subdomain?: string;

  @ApiProperty({ description: '使用的模板ID', required: false })
  @IsOptional()
  @IsInt()
  @Min(1)
  @Type(() => Number)
  templateId?: number;

  @ApiProperty({ description: '网站图标', required: false })
  @IsOptional()
  @IsString()
  favicon?: string;

  @ApiProperty({ description: '网站Logo', required: false })
  @IsOptional()
  @IsString()
  logo?: string;

  @ApiProperty({
    description: 'SEO配置',
    type: 'object',
    required: false,
    example: {
      title: '网站标题',
      description: '网站描述',
      keywords: '关键词1,关键词2',
      author: '作者',
      robots: 'index,follow',
      ogTitle: 'Open Graph标题',
      ogDescription: 'Open Graph描述',
      ogImage: 'Open Graph图片',
    },
  })
  @IsOptional()
  @IsObject()
  seoConfig?: JsonValue;

  @ApiProperty({
    description: '主题配置',
    type: 'object',
    required: false,
    example: {
      primaryColor: '#1976d2',
      secondaryColor: '#dc004e',
      fontSize: 'medium',
      fontFamily: 'Roboto',
      layout: 'standard',
      headerStyle: 'fixed',
      footerStyle: 'minimal',
    },
  })
  @IsOptional()
  @IsObject()
  themeConfig?: JsonValue;

  @ApiProperty({
    description: '网站设置',
    type: 'object',
    required: false,
    example: {
      language: 'zh-CN',
      timezone: 'Asia/Shanghai',
      dateFormat: 'YYYY-MM-DD',
      currency: 'CNY',
      enableComments: true,
      enableAnalytics: true,
      maintenanceMode: false,
    },
  })
  @IsOptional()
  @IsObject()
  settings?: JsonValue;

  @ApiProperty({
    description: '元数据',
    type: 'object',
    required: false,
    example: {
      tags: ['企业网站', '现代设计'],
      category: 'business',
      industry: 'technology',
      targetAudience: 'B2B客户',
    },
  })
  @IsOptional()
  @IsObject()
  metadata?: JsonValue;
}
