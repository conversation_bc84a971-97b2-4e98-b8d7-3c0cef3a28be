import { CurrentTenant } from '@core/decorators/current-tenant.decorator';
import { CurrentUser } from '@core/decorators/current-user.decorator';
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  Query,
  ParseIntPipe,
} from '@nestjs/common';
import {
  ApiTags,
  ApiOperation,
  ApiResponse,
  ApiBearerAuth,
  ApiParam,
  ApiQuery,
} from '@nestjs/swagger';

import { ComponentLibraryService } from './component-library.service';
import { CreateComponentLibraryDto } from './dto/create-component-library.dto';
import { QueryComponentLibraryDto } from './dto/query-component-library.dto';
import { UpdateComponentLibraryDto } from './dto/update-component-library.dto';
import { ComponentLibraryEntity } from './entities/component-library.entity';

@ApiTags('组件库管理')
@ApiBearerAuth()
@Controller('component-library')
export class ComponentLibraryController {
  constructor(private readonly componentLibraryService: ComponentLibraryService) {}

  @Post()
  @ApiOperation({ summary: '创建组件' })
  @ApiResponse({
    status: 201,
    description: '组件创建成功',
    type: ComponentLibraryEntity,
  })
  @ApiResponse({ status: 409, description: '组件名称已存在' })
  async create(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Body() createDto: CreateComponentLibraryDto,
  ): Promise<ComponentLibraryEntity> {
    return this.componentLibraryService.create(tenantId, userId, createDto);
  }

  @Get()
  @ApiOperation({ summary: '获取组件列表' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        data: {
          type: 'array',
          items: { $ref: '#/components/schemas/ComponentLibraryEntity' },
        },
        total: { type: 'number' },
        page: { type: 'number' },
        limit: { type: 'number' },
        totalPages: { type: 'number' },
      },
    },
  })
  async findAll(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Query() queryDto: QueryComponentLibraryDto,
  ) {
    return this.componentLibraryService.findAll(tenantId, userId, queryDto);
  }

  @Get('stats')
  @ApiOperation({ summary: '获取组件统计信息' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'object',
      properties: {
        total: { type: 'number' },
        active: { type: 'number' },
        system: { type: 'number' },
        custom: { type: 'number' },
      },
    },
  })
  async getStats(@CurrentTenant('id') tenantId: number, @CurrentUser('id') userId: number) {
    return this.componentLibraryService.getComponentStats(tenantId, userId);
  }

  @Get('categories')
  @ApiOperation({ summary: '获取组件分类列表' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'array',
      items: { type: 'string' },
    },
  })
  async getCategories(@CurrentTenant('id') tenantId: number) {
    return this.componentLibraryService.getCategories(tenantId);
  }

  @Get('by-category/:category')
  @ApiOperation({ summary: '获取指定分类的组件' })
  @ApiParam({ name: 'category', description: '组件分类' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    schema: {
      type: 'array',
      items: {
        type: 'object',
        properties: {
          id: { type: 'number' },
          name: { type: 'string' },
          displayName: { type: 'string' },
          description: { type: 'string' },
          category: { type: 'string' },
          icon: { type: 'string' },
          thumbnail: { type: 'string' },
          defaultProps: { type: 'object' },
          config: { type: 'object' },
          version: { type: 'string' },
          isBuiltIn: { type: 'boolean' },
        },
      },
    },
  })
  async getComponentsByCategory(
    @CurrentTenant('id') tenantId: number,
    @Param('category') category: string,
  ) {
    return this.componentLibraryService.getComponentsByCategory(tenantId, category);
  }

  @Get(':id')
  @ApiOperation({ summary: '获取单个组件详情' })
  @ApiParam({ name: 'id', description: '组件ID' })
  @ApiResponse({
    status: 200,
    description: '获取成功',
    type: ComponentLibraryEntity,
  })
  @ApiResponse({ status: 404, description: '组件不存在' })
  async findOne(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ComponentLibraryEntity> {
    return this.componentLibraryService.findOne(tenantId, id, userId);
  }

  @Patch(':id')
  @ApiOperation({ summary: '更新组件信息' })
  @ApiParam({ name: 'id', description: '组件ID' })
  @ApiResponse({
    status: 200,
    description: '更新成功',
    type: ComponentLibraryEntity,
  })
  @ApiResponse({ status: 404, description: '组件不存在' })
  @ApiResponse({ status: 409, description: '组件名称冲突' })
  async update(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Param('id', ParseIntPipe) id: number,
    @Body() updateDto: UpdateComponentLibraryDto,
  ): Promise<ComponentLibraryEntity> {
    return this.componentLibraryService.update(tenantId, id, updateDto, userId);
  }

  @Delete(':id')
  @ApiOperation({ summary: '删除组件' })
  @ApiParam({ name: 'id', description: '组件ID' })
  @ApiResponse({ status: 200, description: '删除成功' })
  @ApiResponse({ status: 404, description: '组件不存在' })
  @ApiResponse({ status: 409, description: '系统组件不能删除' })
  async remove(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<{ message: string }> {
    await this.componentLibraryService.remove(tenantId, id, userId);
    return { message: '组件删除成功' };
  }

  @Post(':id/duplicate')
  @ApiOperation({ summary: '复制组件' })
  @ApiParam({ name: 'id', description: '源组件ID' })
  @ApiQuery({ name: 'name', description: '新组件名称' })
  @ApiResponse({
    status: 201,
    description: '复制成功',
    type: ComponentLibraryEntity,
  })
  @ApiResponse({ status: 404, description: '源组件不存在' })
  @ApiResponse({ status: 409, description: '新组件名称已存在' })
  async duplicate(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Param('id', ParseIntPipe) id: number,
    @Query('name') newName: string,
  ): Promise<ComponentLibraryEntity> {
    return this.componentLibraryService.duplicate(tenantId, id, newName, userId);
  }

  @Post(':id/toggle-active')
  @ApiOperation({ summary: '切换组件激活状态' })
  @ApiParam({ name: 'id', description: '组件ID' })
  @ApiResponse({
    status: 200,
    description: '切换成功',
    type: ComponentLibraryEntity,
  })
  @ApiResponse({ status: 404, description: '组件不存在' })
  async toggleActive(
    @CurrentTenant('id') tenantId: number,
    @CurrentUser('id') userId: number,
    @Param('id', ParseIntPipe) id: number,
  ): Promise<ComponentLibraryEntity> {
    return this.componentLibraryService.toggleActive(tenantId, id, userId);
  }
}
