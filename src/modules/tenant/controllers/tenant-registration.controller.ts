import { Body, Controller, Post, Get, Query } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse } from '@nestjs/swagger';

import {
  TenantRegisterDto,
  TenantRegisterResponseDto,
  TenantVerifyDto,
  SubscriptionPlanType,
} from '../dto/tenant-register.dto';
import { TenantRegistrationService } from '../services/tenant-registration.service';

import { Public } from '@/core/auth/decorators/public.decorator';

/**
 * 租户公开注册控制器
 * 处理不需要认证的租户注册相关接口
 */
@ApiTags('公开接口 - 租户注册')
@Controller('public/tenant')
export class TenantRegistrationController {
  constructor(private readonly registrationService: TenantRegistrationService) {}

  /**
   * 租户自助注册
   */
  @Public()
  @Post('register')
  @ApiOperation({ summary: '租户自助注册' })
  @ApiResponse({
    status: 201,
    description: '注册成功',
    type: TenantRegisterResponseDto,
  })
  @ApiResponse({ status: 400, description: '注册信息错误' })
  @ApiResponse({ status: 409, description: '租户代码或域名已存在' })
  async register(@Body() registerDto: TenantRegisterDto): Promise<TenantRegisterResponseDto> {
    return this.registrationService.registerTenant(registerDto);
  }

  /**
   * 验证租户邮箱
   */
  @Public()
  @Post('verify')
  @ApiOperation({ summary: '验证租户邮箱' })
  @ApiResponse({ status: 200, description: '验证成功' })
  @ApiResponse({ status: 400, description: '验证失败' })
  async verify(@Body() verifyDto: TenantVerifyDto): Promise<{ success: boolean; message: string }> {
    return this.registrationService.verifyTenant(verifyDto);
  }

  /**
   * 检查租户代码是否可用
   */
  @Public()
  @Get('check-availability')
  @ApiOperation({ summary: '检查租户代码是否可用' })
  @ApiResponse({ status: 200, description: '检查成功' })
  async checkAvailability(
    @Query('code') tenantCode: string,
    @Query('domain') customDomain?: string,
  ): Promise<{
    codeAvailable: boolean;
    domainAvailable: boolean;
    suggestions?: string[];
  }> {
    return this.registrationService.checkAvailability(tenantCode, customDomain);
  }

  /**
   * 获取可用的订阅计划
   */
  @Public()
  @Get('subscription-plans')
  @ApiOperation({ summary: '获取可用的订阅计划' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getSubscriptionPlans(): Promise<{
    plans: Array<{
      code: SubscriptionPlanType;
      name: string;
      description: string;
      price: number;
      features: string[];
      limits: Record<string, number>;
    }>;
  }> {
    return this.registrationService.getAvailableSubscriptionPlans();
  }

  /**
   * 获取注册进度
   */
  @Public()
  @Get('registration-status')
  @ApiOperation({ summary: '获取注册进度' })
  @ApiResponse({ status: 200, description: '获取成功' })
  async getRegistrationStatus(@Query('code') tenantCode: string): Promise<{
    status: 'pending' | 'verified' | 'active' | 'failed';
    progress: number;
    nextStep?: string;
  }> {
    return this.registrationService.getRegistrationStatus(tenantCode);
  }
}
