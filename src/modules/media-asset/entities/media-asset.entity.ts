import { ApiProperty } from '@nestjs/swagger';
import { JsonValue } from '@prisma/client/runtime/library';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsInt, IsObject, IsDate, IsArray } from 'class-validator';

export class MediaAssetEntity {
  @ApiProperty({ description: '媒体资源ID' })
  id: number;

  @ApiProperty({ description: '文件名' })
  @IsString()
  filename: string;

  @ApiProperty({ description: '原始文件名' })
  @IsString()
  originalName: string;

  @ApiProperty({ description: 'MIME类型' })
  @IsString()
  mimeType: string;

  @ApiProperty({ description: '文件大小(字节)' })
  @IsInt()
  size: number;

  @ApiProperty({ description: '文件URL' })
  @IsString()
  url: string;

  @ApiProperty({ description: '缩略图URL', required: false })
  @IsOptional()
  @IsString()
  thumbnailUrl?: string;

  @ApiProperty({ description: '替代文本', required: false })
  @IsOptional()
  @IsString()
  alt?: string;

  @ApiProperty({
    description: '媒体分类',
    examples: ['image', 'video', 'document', 'audio'],
    default: 'image',
  })
  @IsString()
  category: string;

  @ApiProperty({ description: '标签数组', type: [String] })
  @IsArray()
  @IsString({ each: true })
  tags: string[];

  @ApiProperty({ description: '文件元数据', type: 'object', required: false })
  @IsOptional()
  @IsObject()
  metadata?: JsonValue;

  @ApiProperty({ description: '所属网站ID', required: false })
  @IsOptional()
  @IsInt()
  websiteId?: number;

  @ApiProperty({ description: '租户ID' })
  tenantId: number;

  @ApiProperty({ description: '上传者ID' })
  userId: number;

  @ApiProperty({ description: '创建时间' })
  @IsDate()
  @Type(() => Date)
  createTime: Date;

  @ApiProperty({ description: '更新时间' })
  @IsDate()
  @Type(() => Date)
  updateTime: Date;

  // 关联对象（可选）
  @ApiProperty({ description: '所属网站信息', required: false })
  @IsOptional()
  website?: any;

  @ApiProperty({ description: '上传者信息', required: false })
  @IsOptional()
  user?: any;
}
