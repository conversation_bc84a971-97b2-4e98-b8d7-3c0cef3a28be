import { ApiProperty } from '@nestjs/swagger';
import { Type } from 'class-transformer';
import { IsDate, IsInt, IsOptional } from 'class-validator';

/**
 * 基础实体类
 * 定义所有实体的通用字段和验证规则
 */
export abstract class BaseEntity {
  @ApiProperty({ description: 'ID' })
  id: number;

  @ApiProperty({ description: '创建时间' })
  @IsDate()
  @Type(() => Date)
  createTime: Date;

  @ApiProperty({ description: '更新时间' })
  @IsDate()
  @Type(() => Date)
  updateTime: Date;
}

/**
 * 租户实体基类
 * 包含租户隔离字段
 */
export abstract class BaseTenantEntity extends BaseEntity {
  @ApiProperty({ description: '租户ID' })
  @IsInt()
  tenantId: number;
}

/**
 * 可选租户实体基类
 * 用于支持共享和独立数据库模式的实体
 */
export abstract class BaseOptionalTenantEntity extends BaseEntity {
  @ApiProperty({ description: '租户ID（独立模式可选）', required: false })
  @IsOptional()
  @IsInt()
  tenantId?: number;
}

/**
 * 审计实体基类
 * 包含完整的审计字段
 */
export abstract class BaseAuditEntity extends BaseTenantEntity {
  @ApiProperty({ description: '创建者ID', required: false })
  @IsOptional()
  @IsInt()
  createdBy?: number;

  @ApiProperty({ description: '更新者ID', required: false })
  @IsOptional()
  @IsInt()
  updatedBy?: number;
}
