import {
  Controller,
  Get,
  Post,
  Put,
  Delete,
  Body,
  Param,
  HttpStatus,
  ParseIntPipe,
  UseGuards,
} from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiParam, ApiBearerAuth } from '@nestjs/swagger';

import {
  CreateTenantDto,
  UpdateTenantDto,
  TenantResponseDto,
  TenantListResponseDto,
  TenantStatsResponseDto,
} from './dto/tenant.dto';
import { TenantManagementService } from './tenant-management.service';

import { JwtAuthGuard } from '@/core/auth/guards/jwt-auth.guard';

/**
 * 平台管理 - 租户管理控制器
 *
 * 提供系统管理员级别的租户管理功能：
 * - 租户CRUD操作
 * - 租户统计分析
 * - 数据源配置管理
 */
@ApiTags('平台管理 - 租户')
@Controller('platform/tenants')
@UseGuards(JwtAuthGuard)
@ApiBearerAuth()
export class PlatformTenantController {
  constructor(private readonly tenantManagementService: TenantManagementService) {}

  @Get()
  @ApiOperation({
    summary: '获取租户列表',
    description: '获取所有租户的列表，包含基本信息和统计数据',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '成功获取租户列表',
    type: TenantListResponseDto,
  })
  async findAll(): Promise<TenantListResponseDto> {
    return this.tenantManagementService.findAll();
  }

  @Get('stats')
  @ApiOperation({
    summary: '获取租户统计',
    description: '获取平台租户的统计信息',
  })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '成功获取租户统计',
    type: TenantStatsResponseDto,
  })
  async getStatistics(): Promise<TenantStatsResponseDto> {
    return this.tenantManagementService.getStatistics();
  }

  @Get(':id')
  @ApiOperation({
    summary: '获取租户详情',
    description: '根据ID获取租户的详细信息，包含数据源、订阅等信息',
  })
  @ApiParam({ name: 'id', description: '租户ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '成功获取租户详情',
    type: TenantResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '租户不存在',
  })
  async findOne(@Param('id', ParseIntPipe) id: number): Promise<TenantResponseDto> {
    return this.tenantManagementService.findOne(id);
  }

  @Post()
  @ApiOperation({
    summary: '创建租户',
    description: '创建新的租户，支持共享和独立数据库模式',
  })
  @ApiResponse({
    status: HttpStatus.CREATED,
    description: '成功创建租户',
    type: TenantResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '请求参数错误或租户代码已存在',
  })
  async create(@Body() createTenantDto: CreateTenantDto): Promise<TenantResponseDto> {
    return this.tenantManagementService.create(createTenantDto);
  }

  @Put(':id')
  @ApiOperation({
    summary: '更新租户',
    description: '更新租户基本信息',
  })
  @ApiParam({ name: 'id', description: '租户ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '成功更新租户',
    type: TenantResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '租户不存在',
  })
  @ApiResponse({
    status: HttpStatus.BAD_REQUEST,
    description: '请求参数错误',
  })
  async update(
    @Param('id', ParseIntPipe) id: number,
    @Body() updateTenantDto: UpdateTenantDto,
  ): Promise<TenantResponseDto> {
    return this.tenantManagementService.update(id, updateTenantDto);
  }

  @Delete(':id')
  @ApiOperation({
    summary: '删除租户',
    description: '删除指定租户，将级联删除相关数据',
  })
  @ApiParam({ name: 'id', description: '租户ID' })
  @ApiResponse({
    status: HttpStatus.OK,
    description: '成功删除租户',
    type: TenantResponseDto,
  })
  @ApiResponse({
    status: HttpStatus.NOT_FOUND,
    description: '租户不存在',
  })
  async remove(@Param('id', ParseIntPipe) id: number): Promise<TenantResponseDto> {
    return this.tenantManagementService.remove(id);
  }
}
