import { Injectable, Logger } from '@nestjs/common';

import { PublicDatabaseClient } from './clients/public.client';
import { TenantDatabaseClient } from './clients/tenant.client';

/**
 * 数据库工厂
 *
 * 负责管理和分发数据库客户端：
 * - Public Schema客户端（平台数据）
 * - Tenant Schema客户端（业务数据）
 * - 动态租户数据库连接（Phase 2扩展）
 */
@Injectable()
export class DatabaseFactory {
  private readonly logger = new Logger(DatabaseFactory.name);

  constructor(
    private readonly publicClient: PublicDatabaseClient,
    private readonly tenantClient: TenantDatabaseClient,
  ) {}

  /**
   * 获取Public Schema客户端
   * 用于平台管理功能
   */
  getPublicClient(): PublicDatabaseClient {
    return this.publicClient;
  }

  /**
   * 获取Tenant Schema客户端（共享模式）
   * 当前版本：所有租户共享同一个数据库
   * Phase 2：支持独立数据库
   */
  async getTenantClient(tenantId?: number): Promise<TenantDatabaseClient> {
    // 当前阶段：返回共享客户端
    // 后续Phase 2将根据租户配置返回独立客户端
    return this.tenantClient;
  }

  /**
   * 动态获取租户数据库客户端
   * Phase 2实现：根据租户配置决定使用共享还是独立数据库
   */
  async getDynamicTenantClient(tenantId: number): Promise<TenantDatabaseClient> {
    try {
      // 查询租户数据源配置
      const tenantConfig = await this.publicClient.tenant.findUnique({
        where: { id: tenantId },
        include: { datasource: true },
      });

      if (!tenantConfig) {
        throw new Error(`租户 ${tenantId} 不存在`);
      }

      // 检查是否配置了独立数据库
      if (tenantConfig.datasource && !tenantConfig.datasource.isShared) {
        // Phase 2实现：创建独立数据库连接
        this.logger.warn(`租户 ${tenantId} 配置了独立数据库，但独立模式将在Phase 2实现`);
        throw new Error('独立数据库模式将在Phase 2实现');
      }

      // 返回共享客户端
      return this.tenantClient;
    } catch (error) {
      this.logger.error(`获取租户 ${tenantId} 数据库客户端失败:`, error);
      throw error;
    }
  }

  /**
   * 健康检查
   * 检查所有数据库连接状态
   */
  async healthCheck(): Promise<{
    public: boolean;
    tenant: boolean;
    overall: boolean;
  }> {
    const publicHealth = await this.publicClient.healthCheck();
    const tenantHealth = await this.tenantClient.healthCheck();

    return {
      public: publicHealth,
      tenant: tenantHealth,
      overall: publicHealth && tenantHealth,
    };
  }

  /**
   * 跨Schema事务支持（Phase 2扩展）
   * 目前仅用于共享模式下的跨Schema操作
   */
  async crossSchemaTransaction<T>(
    fn: (clients: { publicDb: PublicDatabaseClient; tenantDb: TenantDatabaseClient }) => Promise<T>,
  ): Promise<T> {
    // 当前简单实现，Phase 2优化为真正的分布式事务
    try {
      return await fn({
        publicDb: this.publicClient,
        tenantDb: this.tenantClient,
      });
    } catch (error) {
      this.logger.error('跨Schema事务执行失败:', error);
      throw error;
    }
  }

  /**
   * 获取租户上下文信息
   * 从Public Schema获取租户配置和权限信息
   */
  async getTenantContext(tenantId: number) {
    const tenantInfo = await this.publicClient.tenant.findUnique({
      where: { id: tenantId },
      include: {
        datasource: true,
        subscriptions: {
          include: { plan: true },
          where: { status: 'active' },
          orderBy: { createdAt: 'desc' },
          take: 1,
        },
        features: {
          where: { enabled: true },
        },
        configs: true,
      },
    });

    if (!tenantInfo) {
      throw new Error(`租户 ${tenantId} 不存在`);
    }

    return {
      tenant: tenantInfo,
      currentSubscription: tenantInfo.subscriptions[0] || null,
      enabledFeatures: tenantInfo.features,
      configs: tenantInfo.configs,
      isSharedDatabase: tenantInfo.datasource?.isShared ?? true,
    };
  }
}
