import { PrismaClient } from '@prisma/client';

const prisma = new PrismaClient();

export async function seedComponentLibrary() {
  console.log('开始创建组件库种子数据...');

  // 基础组件数据
  const components = [
    {
      name: 'hero-banner',
      displayName: '英雄横幅',
      category: 'hero',
      description: '大型横幅组件，适用于首页顶部展示',
      icon: 'hero-icon',
      thumbnail: '/images/components/hero-banner.jpg',
      config: {
        type: 'object',
        properties: {
          title: { type: 'string', default: '欢迎来到我们的网站' },
          subtitle: { type: 'string', default: '提供优质的产品和服务' },
          buttonText: { type: 'string', default: '了解更多' },
          buttonLink: { type: 'string', default: '#' },
          backgroundImage: { type: 'string', default: '/images/hero-bg.jpg' },
          backgroundColor: { type: 'string', default: '#f8f9fa' },
          textColor: { type: 'string', default: '#333333' },
          alignment: {
            type: 'string',
            enum: ['left', 'center', 'right'],
            default: 'center',
          },
        },
        required: ['title'],
      },
      template: {
        title: '欢迎来到我们的网站',
        subtitle: '提供优质的产品和服务',
        buttonText: '了解更多',
        buttonLink: '#',
        backgroundImage: '/images/hero-bg.jpg',
        backgroundColor: '#f8f9fa',
        textColor: '#333333',
        alignment: 'center',
      },
      defaultProps: {
        title: '欢迎来到我们的网站',
        subtitle: '提供优质的产品和服务',
        buttonText: '了解更多',
        buttonLink: '#',
        backgroundImage: '/images/hero-bg.jpg',
        backgroundColor: '#f8f9fa',
        textColor: '#333333',
        alignment: 'center',
      },
      styleSchema: {
        padding: '80px 0',
        minHeight: '500px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
      },
      isBuiltIn: true,
      isActive: true,
      version: '1.0.0',
      sortOrder: 1,
      metadata: {
        author: 'System',
        responsive: true,
        tags: ['hero', 'banner', 'responsive'],
      },
    },
    {
      name: 'text-content',
      displayName: '文本内容',
      category: 'content',
      description: '基础文本内容组件，支持富文本编辑',
      icon: 'text-icon',
      thumbnail: '/images/components/text-content.jpg',
      config: {
        type: 'object',
        properties: {
          content: { type: 'string', default: '<p>这里是文本内容</p>' },
          fontSize: { type: 'string', default: '16px' },
          textColor: { type: 'string', default: '#333333' },
          textAlign: {
            type: 'string',
            enum: ['left', 'center', 'right', 'justify'],
            default: 'left',
          },
          lineHeight: { type: 'string', default: '1.6' },
        },
        required: ['content'],
      },
      template: {
        content: '<p>这里是文本内容，您可以编辑这段文字来展示您想要的信息。</p>',
        fontSize: '16px',
        textColor: '#333333',
        textAlign: 'left',
        lineHeight: '1.6',
      },
      defaultProps: {
        content: '<p>这里是文本内容，您可以编辑这段文字来展示您想要的信息。</p>',
        fontSize: '16px',
        textColor: '#333333',
        textAlign: 'left',
        lineHeight: '1.6',
      },
      styleSchema: {
        padding: '20px',
        margin: '0 auto',
        maxWidth: '800px',
      },
      isBuiltIn: true,
      isActive: true,
      version: '1.0.0',
      sortOrder: 2,
      metadata: {
        author: 'System',
        responsive: true,
        tags: ['text', 'content', 'basic'],
      },
    },
    {
      name: 'image-gallery',
      displayName: '图片画廊',
      category: 'media',
      description: '图片展示组件，支持多种布局模式',
      icon: 'gallery-icon',
      thumbnail: '/images/components/image-gallery.jpg',
      config: {
        type: 'object',
        properties: {
          images: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                url: { type: 'string' },
                alt: { type: 'string' },
                caption: { type: 'string' },
              },
            },
            default: [],
          },
          layout: { type: 'string', enum: ['grid', 'masonry', 'carousel'], default: 'grid' },
          columns: { type: 'number', default: 3 },
          spacing: { type: 'string', default: '10px' },
          showCaptions: { type: 'boolean', default: true },
        },
        required: ['images'],
      },
      template: {
        images: [
          { url: '/images/gallery/1.jpg', alt: '图片1', caption: '图片描述1' },
          { url: '/images/gallery/2.jpg', alt: '图片2', caption: '图片描述2' },
          { url: '/images/gallery/3.jpg', alt: '图片3', caption: '图片描述3' },
        ],
        layout: 'grid',
        columns: 3,
        spacing: '10px',
        showCaptions: true,
      },
      defaultProps: {
        images: [
          { url: '/images/gallery/1.jpg', alt: '图片1', caption: '图片描述1' },
          { url: '/images/gallery/2.jpg', alt: '图片2', caption: '图片描述2' },
          { url: '/images/gallery/3.jpg', alt: '图片3', caption: '图片描述3' },
        ],
        layout: 'grid',
        columns: 3,
        spacing: '10px',
        showCaptions: true,
      },
      styleSchema: {
        padding: '20px',
        display: 'grid',
        gap: '10px',
      },
      isBuiltIn: true,
      isActive: true,
      version: '1.0.0',
      sortOrder: 3,
      metadata: {
        author: 'System',
        responsive: true,
        tags: ['image', 'gallery', 'media'],
      },
    },
    {
      name: 'contact-form',
      displayName: '联系表单',
      category: 'form',
      description: '基础联系表单，包含常用字段',
      icon: 'form-icon',
      thumbnail: '/images/components/contact-form.jpg',
      config: {
        type: 'object',
        properties: {
          title: { type: 'string', default: '联系我们' },
          fields: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                name: { type: 'string' },
                label: { type: 'string' },
                type: { type: 'string' },
                required: { type: 'boolean' },
                placeholder: { type: 'string' },
              },
            },
            default: [],
          },
          submitText: { type: 'string', default: '提交' },
          successMessage: { type: 'string', default: '感谢您的留言，我们会尽快回复！' },
        },
        required: ['title', 'fields'],
      },
      template: {
        title: '联系我们',
        fields: [
          {
            name: 'name',
            label: '姓名',
            type: 'text',
            required: true,
            placeholder: '请输入您的姓名',
          },
          {
            name: 'email',
            label: '邮箱',
            type: 'email',
            required: true,
            placeholder: '请输入您的邮箱',
          },
          {
            name: 'phone',
            label: '电话',
            type: 'tel',
            required: false,
            placeholder: '请输入您的电话',
          },
          {
            name: 'message',
            label: '留言',
            type: 'textarea',
            required: true,
            placeholder: '请输入您的留言',
          },
        ],
        submitText: '提交',
        successMessage: '感谢您的留言，我们会尽快回复！',
      },
      defaultProps: {
        title: '联系我们',
        fields: [
          {
            name: 'name',
            label: '姓名',
            type: 'text',
            required: true,
            placeholder: '请输入您的姓名',
          },
          {
            name: 'email',
            label: '邮箱',
            type: 'email',
            required: true,
            placeholder: '请输入您的邮箱',
          },
          {
            name: 'phone',
            label: '电话',
            type: 'tel',
            required: false,
            placeholder: '请输入您的电话',
          },
          {
            name: 'message',
            label: '留言',
            type: 'textarea',
            required: true,
            placeholder: '请输入您的留言',
          },
        ],
        submitText: '提交',
        successMessage: '感谢您的留言，我们会尽快回复！',
      },
      styleSchema: {
        padding: '40px',
        backgroundColor: '#f8f9fa',
        borderRadius: '8px',
      },
      isBuiltIn: true,
      isActive: true,
      version: '1.0.0',
      sortOrder: 4,
      metadata: {
        author: 'System',
        responsive: true,
        tags: ['form', 'contact', 'interactive'],
      },
    },
    {
      name: 'navigation-menu',
      displayName: '导航菜单',
      category: 'navigation',
      description: '网站导航菜单组件，支持多级菜单',
      icon: 'nav-icon',
      thumbnail: '/images/components/navigation-menu.jpg',
      config: {
        type: 'object',
        properties: {
          logo: { type: 'string', default: '/images/logo.png' },
          logoText: { type: 'string', default: '网站名称' },
          menuItems: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                label: { type: 'string' },
                link: { type: 'string' },
                children: { type: 'array' },
              },
            },
            default: [],
          },
          style: { type: 'string', enum: ['horizontal', 'vertical'], default: 'horizontal' },
          backgroundColor: { type: 'string', default: '#ffffff' },
          textColor: { type: 'string', default: '#333333' },
        },
        required: ['menuItems'],
      },
      template: {
        logo: '/images/logo.png',
        logoText: '网站名称',
        menuItems: [
          { label: '首页', link: '/' },
          { label: '关于我们', link: '/about' },
          { label: '产品服务', link: '/services' },
          { label: '联系我们', link: '/contact' },
        ],
        style: 'horizontal',
        backgroundColor: '#ffffff',
        textColor: '#333333',
      },
      defaultProps: {
        logo: '/images/logo.png',
        logoText: '网站名称',
        menuItems: [
          { label: '首页', link: '/' },
          { label: '关于我们', link: '/about' },
          { label: '产品服务', link: '/services' },
          { label: '联系我们', link: '/contact' },
        ],
        style: 'horizontal',
        backgroundColor: '#ffffff',
        textColor: '#333333',
      },
      styleSchema: {
        padding: '10px 20px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'space-between',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
      },
      isBuiltIn: true,
      isActive: true,
      version: '1.0.0',
      sortOrder: 5,
      metadata: {
        author: 'System',
        responsive: true,
        tags: ['navigation', 'menu', 'header'],
      },
    },
    {
      name: 'footer-section',
      displayName: '页脚区域',
      category: 'footer',
      description: '网站页脚组件，包含链接和版权信息',
      icon: 'footer-icon',
      thumbnail: '/images/components/footer-section.jpg',
      config: {
        type: 'object',
        properties: {
          companyName: { type: 'string', default: '公司名称' },
          copyright: { type: 'string', default: '© 2024 版权所有' },
          links: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                title: { type: 'string' },
                items: {
                  type: 'array',
                  items: {
                    type: 'object',
                    properties: {
                      label: { type: 'string' },
                      link: { type: 'string' },
                    },
                  },
                },
              },
            },
            default: [],
          },
          socialLinks: {
            type: 'array',
            items: {
              type: 'object',
              properties: {
                platform: { type: 'string' },
                url: { type: 'string' },
                icon: { type: 'string' },
              },
            },
            default: [],
          },
          backgroundColor: { type: 'string', default: '#333333' },
          textColor: { type: 'string', default: '#ffffff' },
        },
        required: ['companyName', 'copyright'],
      },
      template: {
        companyName: '公司名称',
        copyright: '© 2024 版权所有',
        links: [
          {
            title: '产品',
            items: [
              { label: '功能特性', link: '/features' },
              { label: '价格方案', link: '/pricing' },
              { label: '案例展示', link: '/cases' },
            ],
          },
          {
            title: '支持',
            items: [
              { label: '帮助中心', link: '/help' },
              { label: '联系我们', link: '/contact' },
              { label: '意见反馈', link: '/feedback' },
            ],
          },
        ],
        socialLinks: [
          { platform: '微信', url: '#', icon: 'wechat' },
          { platform: '微博', url: '#', icon: 'weibo' },
        ],
        backgroundColor: '#333333',
        textColor: '#ffffff',
      },
      defaultProps: {
        companyName: '公司名称',
        copyright: '© 2024 版权所有',
        links: [
          {
            title: '产品',
            items: [
              { label: '功能特性', link: '/features' },
              { label: '价格方案', link: '/pricing' },
              { label: '案例展示', link: '/cases' },
            ],
          },
          {
            title: '支持',
            items: [
              { label: '帮助中心', link: '/help' },
              { label: '联系我们', link: '/contact' },
              { label: '意见反馈', link: '/feedback' },
            ],
          },
        ],
        socialLinks: [
          { platform: '微信', url: '#', icon: 'wechat' },
          { platform: '微博', url: '#', icon: 'weibo' },
        ],
        backgroundColor: '#333333',
        textColor: '#ffffff',
      },
      styleSchema: {
        padding: '40px 20px 20px',
        backgroundColor: '#333333',
        color: '#ffffff',
      },
      isBuiltIn: true,
      isActive: true,
      version: '1.0.0',
      sortOrder: 6,
      metadata: {
        author: 'System',
        responsive: true,
        tags: ['footer', 'links', 'social'],
      },
    },
  ];

  // 创建默认组件（租户ID为1）
  console.log('为默认租户创建组件...');
  for (const component of components) {
    try {
      await prisma.componentLibrary.upsert({
        where: {
          name_tenantId: {
            name: component.name,
            tenantId: 1, // 使用默认租户ID
          },
        },
        update: {},
        create: {
          ...component,
          tenantId: 1,
        },
      });
      console.log(`✓ 创建组件: ${component.displayName}`);
    } catch (error) {
      console.error(`✗ 创建组件 ${component.displayName} 失败:`, error);
    }
  }

  console.log('组件库种子数据创建完成！');
}

if (require.main === module) {
  seedComponentLibrary()
    .catch(e => {
      console.error(e);
      process.exit(1);
    })
    .finally(async () => {
      await prisma.$disconnect();
    });
}
