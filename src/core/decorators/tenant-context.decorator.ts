import { createParamDecorator, ExecutionContext } from '@nestjs/common';

import { RequestTenantContext } from '../database/interfaces/tenant-context.interface';

/**
 * 租户上下文装饰器
 *
 * 从请求对象中提取租户信息
 * 支持多种租户识别方式：
 * - HTTP头部 (X-Tenant-Id, X-Tenant-Code)
 * - 域名解析
 * - 路径参数
 */
export const TenantContext = createParamDecorator(
  (data: keyof RequestTenantContext | undefined, ctx: ExecutionContext): any => {
    const request = ctx.switchToHttp().getRequest();

    // 从中间件设置的租户信息中获取
    const tenantContext: RequestTenantContext = {
      tenantId: request.tenantId,
      tenantCode: request.tenantCode,
      userId: request.user?.id,
      userRoles: request.user?.roles,
      isSystemAdmin: request.user?.userType === 'SYSTEM',
    };

    // 如果指定了特定字段，返回该字段值
    if (data) {
      return tenantContext[data];
    }

    // 否则返回完整的上下文
    return tenantContext;
  },
);

/**
 * 简化的租户ID装饰器
 */
export const TenantId = createParamDecorator(
  (_data: unknown, ctx: ExecutionContext): number | undefined => {
    const request = ctx.switchToHttp().getRequest();
    return request.tenantId;
  },
);

/**
 * 简化的租户代码装饰器
 */
export const TenantCode = createParamDecorator(
  (_data: unknown, ctx: ExecutionContext): string | undefined => {
    const request = ctx.switchToHttp().getRequest();
    return request.tenantCode;
  },
);
