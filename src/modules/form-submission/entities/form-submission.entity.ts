import { ApiProperty } from '@nestjs/swagger';
import { JsonValue } from '@prisma/client/runtime/library';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsInt, IsObject, IsDate, IsIP } from 'class-validator';

export class FormSubmissionEntity {
  @ApiProperty({ description: '提交记录ID' })
  id: number;

  @ApiProperty({
    description: '表单数据',
    type: 'object',
    example: {
      name: '张三',
      email: '<EMAIL>',
      phone: '13800138000',
      company: '示例公司',
      message: '我对您的产品很感兴趣，希望了解更多信息。',
    },
  })
  @IsObject()
  data: JsonValue;

  @ApiProperty({
    description: '提交状态',
    enum: ['pending', 'read', 'replied', 'archived'],
    default: 'pending',
  })
  @IsString()
  status: string;

  @ApiProperty({ description: '提交者IP地址', required: false })
  @IsOptional()
  @IsIP()
  ipAddress?: string;

  @ApiProperty({ description: '提交者用户代理', required: false })
  @IsOptional()
  @IsString()
  userAgent?: string;

  @ApiProperty({ description: '来源页面URL', required: false })
  @IsOptional()
  @IsString()
  referrer?: string;

  @ApiProperty({
    description: '额外信息',
    type: 'object',
    required: false,
    example: {
      source: 'contact-form',
      campaign: 'spring-promotion',
      utm_source: 'google',
      utm_medium: 'cpc',
      utm_campaign: 'brand',
    },
  })
  @IsOptional()
  @IsObject()
  metadata?: JsonValue;

  @ApiProperty({ description: '备注', required: false })
  @IsOptional()
  @IsString()
  notes?: string;

  @ApiProperty({ description: '所属表单ID' })
  formId: number;

  @ApiProperty({ description: '租户ID' })
  tenantId: number;

  @ApiProperty({ description: '创建时间' })
  @IsDate()
  @Type(() => Date)
  createTime: Date;

  @ApiProperty({ description: '更新时间' })
  @IsDate()
  @Type(() => Date)
  updateTime: Date;

  // 关联对象（可选）
  @ApiProperty({ description: '表单信息', required: false })
  @IsOptional()
  form?: any;
}
