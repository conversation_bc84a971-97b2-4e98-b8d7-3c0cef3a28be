import { Injectable } from '@nestjs/common';
import { OpenAPIObject } from '@nestjs/swagger';

/**
 * 操作对象接口
 */
interface OperationObject {
  tags?: string[];
  summary?: string;
  description?: string;
  parameters?: any[];
  requestBody?: any;
  responses?: any;
  [key: string]: any;
}

/**
 * 路径项目对象接口
 */
interface PathItemObject {
  [method: string]: OperationObject | any;
}

/**
 * 路径数据接口
 */
interface PathData {
  path: string;
  method: string;
  operation: OperationObject;
}

/**
 * Swagger转Markdown服务
 * 将Swagger文档转换为Markdown格式
 */
@Injectable()
export class SwaggerMarkdownService {
  /**
   * 将Swagger文档转换为Markdown
   * @param document Swagger文档对象
   * @returns Markdown字符串
   */
  convertToMarkdown(document: OpenAPIObject): string {
    const markdown: string[] = [];

    // 文档标题和描述
    markdown.push(`# ${document.info.title}`);
    markdown.push('');
    if (document.info.description) {
      markdown.push(document.info.description);
      markdown.push('');
    }
    markdown.push(`**版本**: ${document.info.version}`);
    markdown.push('');

    // 添加统一返回格式说明
    markdown.push('## 统一返回格式');
    markdown.push('');
    markdown.push('所有API接口都会返回以下统一格式的响应：');
    markdown.push('');
    markdown.push('```json');
    markdown.push('{');
    markdown.push('  "code": 0,           // 状态码：0-成功，其他值-失败');
    markdown.push('  "data": {},          // 响应数据，类型根据具体接口而定');
    markdown.push('  "message": "操作成功" // 响应消息');
    markdown.push('}');
    markdown.push('```');
    markdown.push('');

    // 状态码说明
    markdown.push('### 状态码说明');
    markdown.push('');
    markdown.push('| 状态码 | 说明 |');
    markdown.push('|--------|------|');
    markdown.push('| 0 | 操作成功 |');
    markdown.push('| -1 | 操作失败 |');
    markdown.push('| 400 | 参数错误 |');
    markdown.push('| 401 | 未授权 |');
    markdown.push('| 403 | 禁止访问 |');
    markdown.push('| 404 | 资源不存在 |');
    markdown.push('| 500 | 服务器内部错误 |');
    markdown.push('');

    // 认证说明
    markdown.push('## 认证方式');
    markdown.push('');
    markdown.push('大部分API需要Bearer Token认证，请在请求头中添加：');
    markdown.push('```');
    markdown.push('Authorization: Bearer <your_token>');
    markdown.push('```');
    markdown.push('');

    // 按标签分组API
    const tagGroups = this.groupPathsByTags(document);

    for (const [tag, paths] of Object.entries(tagGroups)) {
      markdown.push(`## ${tag}`);
      markdown.push('');

      for (const pathData of paths) {
        const { path, method, operation } = pathData;

        // API标题
        markdown.push(`### ${operation.summary || `${method.toUpperCase()} ${path}`}`);
        markdown.push('');

        // 描述
        if (operation.description) {
          markdown.push(operation.description);
          markdown.push('');
        }

        // 请求信息
        markdown.push(`**请求方式**: \`${method.toUpperCase()}\``);
        markdown.push('');
        markdown.push(`**请求路径**: \`${path}\``);
        markdown.push('');

        // 请求参数
        if (operation.parameters && operation.parameters.length > 0) {
          markdown.push('**请求参数**:');
          markdown.push('');
          markdown.push('| 参数名 | 类型 | 位置 | 必填 | 说明 |');
          markdown.push('|--------|------|------|------|------|');

          for (const param of operation.parameters) {
            const required = param.required ? '是' : '否';
            const location = param.in;
            const type = param.schema?.type || 'string';
            const description = param.description || '';
            markdown.push(
              `| ${param.name} | ${type} | ${location} | ${required} | ${description} |`,
            );
          }
          markdown.push('');
        }

        // 请求体
        if (operation.requestBody) {
          markdown.push('**请求体**:');
          markdown.push('');
          const content = operation.requestBody.content;
          if (content && content['application/json']) {
            const schema = content['application/json'].schema;
            if (schema) {
              markdown.push('```json');
              markdown.push(this.generateSchemaExample(schema, document));
              markdown.push('```');
              markdown.push('');
            }
          }
        }

        // 响应示例
        if (operation.responses) {
          markdown.push('**响应示例**:');
          markdown.push('');

          const successResponse = operation.responses['200'] || operation.responses['201'];
          if (successResponse) {
            const content = successResponse.content;
            if (content && content['application/json']) {
              const schema = content['application/json'].schema;
              markdown.push('```json');
              markdown.push('{');
              markdown.push('  "code": 0,');
              markdown.push('  "message": "操作成功",');
              if (schema) {
                const dataExample = this.generateSchemaExample(schema, document);
                markdown.push(`  "data": ${dataExample}`);
              } else {
                markdown.push('  "data": null');
              }
              markdown.push('}');
              markdown.push('```');
            }
          }
          markdown.push('');
        }

        markdown.push('---');
        markdown.push('');
      }
    }

    return markdown.join('\n');
  }

  /**
   * 按标签分组API路径
   */
  private groupPathsByTags(document: OpenAPIObject): Record<string, PathData[]> {
    const groups: Record<string, PathData[]> = {};

    for (const [path, pathItem] of Object.entries(document.paths || {})) {
      const pathItemObj = pathItem as PathItemObject;

      for (const [method, operation] of Object.entries(pathItemObj)) {
        if (typeof operation === 'object' && operation && 'tags' in operation) {
          const operationObj = operation as OperationObject;
          const tag = operationObj.tags?.[0] || '其他';

          if (!groups[tag]) {
            groups[tag] = [];
          }

          groups[tag].push({
            path,
            method,
            operation: operationObj,
          });
        }
      }
    }

    return groups;
  }

  /**
   * 根据Schema生成示例
   */
  private generateSchemaExample(schema: any, document: OpenAPIObject, depth = 0): string {
    if (depth > 3) return '{}'; // 防止无限递归

    if (schema.$ref) {
      const refPath = schema.$ref.replace('#/components/schemas/', '');
      const refSchema = document.components?.schemas?.[refPath];
      if (refSchema) {
        return this.generateSchemaExample(refSchema, document, depth + 1);
      }
    }

    if (schema.type === 'object' || schema.properties) {
      const obj: any = {};
      if (schema.properties) {
        for (const [propName, propSchema] of Object.entries(schema.properties)) {
          obj[propName] = this.generateSchemaExample(propSchema, document, depth + 1);
        }
      }
      return JSON.stringify(obj, null, 2);
    }

    if (schema.type === 'array') {
      const itemExample = schema.items
        ? this.generateSchemaExample(schema.items, document, depth + 1)
        : '{}';
      return `[${itemExample}]`;
    }

    // 基础类型示例
    switch (schema.type) {
      case 'string':
        return schema.example ? `"${schema.example}"` : '"string"';
      case 'number':
      case 'integer':
        return schema.example ? schema.example.toString() : '0';
      case 'boolean':
        return schema.example ? schema.example.toString() : 'true';
      default:
        return 'null';
    }
  }
}
