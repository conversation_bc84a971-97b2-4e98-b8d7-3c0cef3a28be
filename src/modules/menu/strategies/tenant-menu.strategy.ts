import {
  Injectable,
  Logger,
  NotFoundException,
  ConflictException,
  Inject,
  BadRequestException,
} from '@nestjs/common';

import { MenuStrategyType } from './menu-strategy.factory';
import { IMenuStrategy } from './menu-strategy.interface';
import { CreateMenuDto, MenuDto, MenuTreeDto, UpdateMenuDto } from '../dto/menu.dto';

import { ApiCode, ApiMessage } from '@/core/common/constants/api-code.constant';
import { DateFormatUtil } from '@/core/common/utils/date-format.util';
import { DatabaseFactory } from '@/core/database/database.factory';
/**
 * 租户菜单策略
 * 实现租户菜单相关的操作
 */
@Injectable()
export class TenantMenuStrategy implements IMenuStrategy {
  private readonly logger = new Logger(TenantMenuStrategy.name);

  constructor(@Inject(TENANT_PRISMA_SERVICE) private readonly prisma: any) {}

  /**
   * 获取策略类型
   * @returns 策略类型
   */
  getType(): string {
    return MenuStrategyType.TENANT;
  }

  /**
   * 创建租户菜单
   * @param createMenuDto 创建菜单DTO
   * @param tenantId 租户ID
   * @returns 创建的菜单
   */
  async create(createMenuDto: CreateMenuDto, tenantId?: string): Promise<MenuDto> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    const { name, path, meta, ...rest } = createMenuDto;

    // 检查路径是否已存在
    const existingPath = await this.prisma.menu.findFirst({
      where: {
        path,
        tenantId,
      },
    });

    if (existingPath) {
      throw new ConflictException(`菜单路径 ${path} 已存在`);
    }

    // 创建菜单
    const menu = await this.prisma.menu.create({
      data: {
        name,
        path,
        meta: meta ? JSON.stringify(meta) : '{}',
        tenantId,
        ...rest,
      },
    });

    this.logger.log(`创建租户 ${tenantId} 菜单: ${name}`);

    // 转换返回结果
    return this.transformMenuData(menu);
  }

  /**
   * 获取所有租户菜单
   * @param tenantId 租户ID
   * @returns 菜单列表
   */
  async findAll(tenantId?: string): Promise<MenuDto[]> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    const menus = await this.prisma.menu.findMany({
      where: { tenantId },
      orderBy: { orderNo: 'asc' },
    });

    return this.buildMenuTree(menus);
  }

  /**
   * 获取租户菜单树
   * @param tenantId 租户ID
   * @param userId 用户ID（可选，用于过滤权限）
   * @returns 菜单树
   */
  async getMenuTree(tenantId?: string, userId?: number): Promise<MenuTreeDto[]> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    this.logger.log(`获取租户菜单树, 租户ID: ${tenantId}, 用户ID: ${userId || '无'}`);

    // 基本查询条件
    const where: any = {
      tenantId,
      status: 1,
    };

    // 如果提供了用户ID，则只返回用户有权限的菜单
    if (userId) {
      // 获取用户角色
      const userRoles = await this.prisma.userRole.findMany({
        where: { userId, tenantId: parseInt(tenantId) },
        select: { roleId: true },
      });

      const roleIds = userRoles.map(ur => ur.roleId);
      this.logger.log(`用户角色IDs: ${roleIds.join(', ')}`);

      // 获取角色菜单
      const roleMenus = await this.prisma.roleMenu.findMany({
        where: {
          roleId: { in: roleIds },
          tenantId: parseInt(tenantId),
        },
        select: { menuId: true },
      });

      const menuIds = roleMenus.map(rm => rm.menuId);
      this.logger.log(`角色菜单IDs: ${menuIds.join(', ')}`);

      // 只返回用户有权限的菜单
      if (menuIds.length > 0) {
        where.id = { in: menuIds };
      } else {
        // 如果用户没有任何菜单权限，则返回空数组
        this.logger.log(`用户没有任何菜单权限，返回空数组`);
        return [];
      }
    }

    // 添加基于功能的菜单过滤
    // 只返回租户已启用功能关联的菜单
    where.OR = [
      { featureCode: null }, // 没有关联功能的基础菜单
      {
        featureCode: {
          not: null,
          // 这里不需要额外过滤，因为在同步菜单时已经处理了
          // 只有启用的功能对应的菜单才会被设置为status=1
        },
      },
    ];

    this.logger.log(`查询条件: ${JSON.stringify(where)}`);

    const menus = await this.prisma.menu.findMany({
      where,
      orderBy: { orderNo: 'asc' },
    });

    this.logger.log(`查询到 ${menus.length} 个菜单`);

    return this.buildFrontendMenuTree(menus);
  }

  /**
   * 获取指定ID的租户菜单
   * @param id 菜单ID
   * @param tenantId 租户ID
   * @returns 菜单信息
   */
  async findOne(id: number, tenantId?: string): Promise<MenuDto> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    const menu = await this.prisma.menu.findFirst({
      where: {
        id,
        tenantId,
      },
    });

    if (!menu) {
      throw new NotFoundException(`菜单ID ${id} 不存在`);
    }

    // 转换返回结果
    return this.transformMenuData(menu);
  }

  /**
   * 更新租户菜单
   * @param id 菜单ID
   * @param updateMenuDto 更新菜单DTO
   * @param tenantId 租户ID
   * @returns 更新后的菜单
   */
  async update(id: number, updateMenuDto: UpdateMenuDto, tenantId?: string): Promise<MenuDto> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    const { name, path, meta, ...rest } = updateMenuDto;

    // 检查菜单是否存在
    await this.findOne(id, tenantId);

    // 检查路径是否已被其他菜单使用
    if (path) {
      const existingPath = await this.prisma.menu.findFirst({
        where: {
          path,
          tenantId,
          id: { not: id },
        },
      });

      if (existingPath) {
        throw new ConflictException(`菜单路径 ${path} 已被其他菜单使用`);
      }
    }

    // 更新菜单
    const menu = await this.prisma.menu.update({
      where: { id },
      data: {
        name,
        path,
        meta: meta ? JSON.stringify(meta) : undefined,
        ...rest,
      },
    });

    this.logger.log(`更新租户 ${tenantId} 菜单: ${id}`);

    // 转换返回结果
    return this.transformMenuData(menu);
  }

  /**
   * 删除租户菜单
   * @param id 菜单ID
   * @param tenantId 租户ID
   * @returns 删除结果
   */
  async remove(id: number, tenantId?: string): Promise<{ success: boolean }> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    // 检查菜单是否存在
    await this.findOne(id, tenantId);

    // 检查是否有子菜单
    const children = await this.prisma.menu.findMany({
      where: { pid: id, tenantId },
    });

    if (children.length > 0) {
      throw new ConflictException(`菜单ID ${id} 有子菜单，无法删除`);
    }

    // 删除菜单
    await this.prisma.menu.delete({
      where: { id },
    });

    this.logger.log(`删除租户 ${tenantId} 菜单: ${id}`);

    return { success: true };
  }

  /**
   * 检查菜单名称是否存在
   * @param name 菜单名称
   * @param tenantId 租户ID
   * @param id 排除的菜单ID（可选）
   * @returns 是否存在
   */
  async isNameExists(name: string, tenantId?: string, id?: number): Promise<boolean> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    const menu = await this.prisma.menu.findFirst({
      where: {
        name,
        tenantId,
        ...(id ? { id: { not: id } } : {}),
      },
    });

    return !!menu;
  }

  /**
   * 检查菜单路径是否存在
   * @param path 菜单路径
   * @param tenantId 租户ID
   * @param id 排除的菜单ID（可选）
   * @returns 是否存在
   */
  async isPathExists(path: string, tenantId?: string, id?: number): Promise<boolean> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    const menu = await this.prisma.menu.findFirst({
      where: {
        path,
        tenantId,
        ...(id ? { id: { not: id } } : {}),
      },
    });

    return !!menu;
  }

  /**
   * 根据ID查询菜单
   * @param id 菜单ID
   * @param tenantId 租户ID
   * @returns 菜单信息
   */
  async findById(id: number, tenantId?: string): Promise<MenuDto | null> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    const menu = await this.prisma.menu.findFirst({
      where: {
        id,
        tenantId,
      },
    });

    if (!menu) {
      return null;
    }

    return this.transformMenuData(menu);
  }

  /**
   * 检查是否有子菜单
   * @param parentId 父菜单ID
   * @param tenantId 租户ID
   * @returns 是否有子菜单
   */
  async hasChildren(parentId: number, tenantId?: string): Promise<boolean> {
    if (!tenantId) {
      throw new BadRequestException(ApiMessage[ApiCode.PARAM_ERROR]);
    }

    const count = await this.prisma.menu.count({
      where: {
        pid: parentId,
        tenantId,
      },
    });

    return count > 0;
  }

  /**
   * 构建菜单树
   * @param menus 菜单列表
   * @param pid 父菜单ID
   * @returns 菜单树
   */
  private buildMenuTree(menus: any[], pid: number = null): MenuDto[] {
    const result: MenuDto[] = [];

    menus.forEach(menu => {
      if (menu.pid === pid) {
        // 转换菜单数据
        const transformedMenu = this.transformMenuData(menu);

        // 递归处理子菜单
        const children = this.buildMenuTree(menus, menu.id);
        if (children.length > 0) {
          transformedMenu.children = children;
        }

        result.push(transformedMenu);
      }
    });

    return result;
  }

  /**
   * 构建前端菜单树
   * @param menus 菜单列表
   * @param pid 父菜单ID
   * @returns 菜单树
   */
  private buildFrontendMenuTree(menus: any[], pid: number = null): MenuTreeDto[] {
    const result: MenuTreeDto[] = [];

    menus.forEach(menu => {
      if (menu.pid === pid) {
        // 转换菜单数据
        const transformedMenu = this.transformFrontendMenuData(menu);

        // 递归处理子菜单
        const children = this.buildFrontendMenuTree(menus, menu.id);
        if (children.length > 0) {
          transformedMenu.children = children;
        }

        result.push(transformedMenu);
      }
    });

    return result;
  }

  /**
   * 转换菜单数据
   * @param menu 菜单数据
   * @returns 转换后的菜单数据
   */
  private transformMenuData(menu: any): MenuDto {
    let meta: any = { title: menu.name }; // 默认使用菜单名称作为标题
    try {
      if (menu.meta) {
        const parsedMeta = JSON.parse(menu.meta);
        meta = {
          title: parsedMeta.title || menu.name, // 确保有标题
          icon: parsedMeta.icon,
          orderNo: parsedMeta.orderNo,
          hideMenu: parsedMeta.hideMenu,
          ignoreAuth: parsedMeta.ignoreAuth,
          hideBreadcrumb: parsedMeta.hideBreadcrumb,
          hideChildrenInMenu: parsedMeta.hideChildrenInMenu,
          currentActiveMenu: parsedMeta.currentActiveMenu,
        };
      }
    } catch (error) {
      this.logger.error(`解析菜单元数据失败: ${error.message}`);
    }

    return {
      id: menu.id,
      name: menu.name,
      path: menu.path,
      component: menu.component,
      redirect: menu.redirect,
      pid: menu.pid,
      type: menu.type,
      icon: menu.icon,
      permission: menu.permission,
      orderNo: menu.orderNo,
      status: menu.status,
      meta,
      createdAt: menu.createdAt,
      updatedAt: menu.updatedAt,
    };
  }

  /**
   * 转换前端菜单数据
   * @param menu 菜单数据
   * @returns 转换后的前端菜单数据
   */
  private transformFrontendMenuData(menu: any): MenuTreeDto {
    let meta = {};
    try {
      meta = menu.meta ? JSON.parse(menu.meta) : {};
    } catch (error) {
      this.logger.error(`解析菜单元数据失败: ${error.message}`);
    }

    return {
      id: menu.id.toString(),
      name: menu.name,
      path: menu.path,
      component: menu.component,
      redirect: menu.redirect,
      meta: meta as any,
    };
  }
}
