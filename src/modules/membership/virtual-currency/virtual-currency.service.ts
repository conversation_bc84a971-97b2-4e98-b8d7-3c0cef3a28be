import { Injectable, Inject } from '@nestjs/common';

import {
  CreateVirtualCurrencyTypeDto,
  UpdateVirtualCurrencyTypeDto,
  QueryVirtualCurrencyTypeDto,
  RechargeVirtualCurrencyDto,
  ConsumeVirtualCurrencyDto,
  QueryUserWalletDto,
  QueryTransactionDto,
  TransactionType,
  FeatureCurrencyCostDto,
} from './dto/virtual-currency.dto';

import { BaseService } from '@/core/common/base/base.service';
import {
  TenantPrismaService,
  TENANT_PRISMA_SERVICE,
} from '@/core/database/prisma/tenant-prisma.service';
import { PaginationOptions } from '@/core/pagination-options/pagination-options';

/**
 * 虚拟币服务
 * 管理虚拟币类型、用户钱包和交易记录
 */
@Injectable()
export class VirtualCurrencyService extends BaseService {
  constructor(@Inject(TENANT_PRISMA_SERVICE) private readonly tenantPrisma: TenantPrismaService) {
    super(VirtualCurrencyService.name);
  }

  /**
   * 创建虚拟币类型
   * @param createDto 创建数据
   * @param tenantId 租户ID
   * @returns 创建的虚拟币类型
   */
  async createCurrencyType(createDto: CreateVirtualCurrencyTypeDto, tenantId: string) {
    try {
      // 检查代码是否已存在
      const exists = await this.isCodeExists(createDto.code, tenantId);
      if (exists) {
        this.validationError(`虚拟币代码 ${createDto.code} 已存在`);
      }

      const now = new Date();

      // 创建虚拟币类型
      const currencyType = await (this.tenantPrisma as any).virtualCurrencyType.create({
        data: {
          code: createDto.code,
          name: createDto.name,
          description: createDto.description,
          icon: createDto.icon,
          exchangeRate: createDto.exchangeRate,
          isActive: createDto.isActive ?? true,
          metadata: createDto.metadata || {},
          tenantId,
          createTime: now,
          updateTime: now,
        },
      });

      return currencyType;
    } catch (error) {
      this.logError('创建虚拟币类型失败', error);
      throw error;
    }
  }

  /**
   * 查询虚拟币类型列表
   * @param queryDto 查询参数
   * @param options 分页选项
   * @param tenantId 租户ID
   * @returns 虚拟币类型列表
   */
  async findAllCurrencyTypes(
    queryDto: QueryVirtualCurrencyTypeDto,
    options: PaginationOptions,
    tenantId: string,
  ) {
    try {
      const where: any = { tenantId };

      if (queryDto.isActive !== undefined) {
        where.isActive = queryDto.isActive;
      }

      const [items, total] = await Promise.all([
        (this.tenantPrisma as any).virtualCurrencyType.findMany({
          where,
          orderBy: { createTime: 'desc' },
          skip: options.skip,
          take: options.take,
        }),
        (this.tenantPrisma as any).virtualCurrencyType.count({ where }),
      ]);

      return {
        items,
        total,
        page: options.page,
        pageSize: options.pageSize,
        totalPages: Math.ceil(total / options.pageSize),
      };
    } catch (error) {
      this.logError('查询虚拟币类型列表失败', error);
      throw error;
    }
  }

  /**
   * 查询单个虚拟币类型
   * @param id 虚拟币类型ID
   * @param tenantId 租户ID
   * @returns 虚拟币类型信息
   */
  async findOneCurrencyType(id: number, tenantId: string) {
    try {
      const currencyType = await (this.tenantPrisma as any).virtualCurrencyType.findFirst({
        where: { id, tenantId },
      });

      if (!currencyType) {
        this.notFound('虚拟币类型', id);
      }

      return currencyType;
    } catch (error) {
      this.logError(`查询虚拟币类型失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 更新虚拟币类型
   * @param id 虚拟币类型ID
   * @param updateDto 更新数据
   * @param tenantId 租户ID
   * @returns 更新后的虚拟币类型
   */
  async updateCurrencyType(id: number, updateDto: UpdateVirtualCurrencyTypeDto, tenantId: string) {
    try {
      // 检查虚拟币类型是否存在
      const currencyType = await (this.tenantPrisma as any).virtualCurrencyType.findFirst({
        where: { id, tenantId },
      });

      if (!currencyType) {
        this.notFound('虚拟币类型', id);
      }

      // 如果更新代码，检查是否存在重复
      if (updateDto.code && updateDto.code !== currencyType.code) {
        const exists = await this.isCodeExists(updateDto.code, tenantId);
        if (exists) {
          this.validationError(`虚拟币代码 ${updateDto.code} 已存在`);
        }
      }

      // 更新虚拟币类型
      const updated = await (this.tenantPrisma as any).virtualCurrencyType.update({
        where: { id },
        data: {
          ...updateDto,
          updateTime: new Date(),
        },
      });

      return updated;
    } catch (error) {
      this.logError(`更新虚拟币类型失败，ID: ${id}`, error);
      throw error;
    }
  }

  /**
   * 用户充值虚拟币
   * @param rechargeDto 充值数据
   * @param tenantId 租户ID
   * @returns 充值结果
   */
  async rechargeUserWallet(rechargeDto: RechargeVirtualCurrencyDto, tenantId: string) {
    try {
      const { userId, currencyTypeId, amount, remark, orderNo, metadata } = rechargeDto;

      // 检查用户是否存在
      const user = await (this.tenantPrisma as any).user.findFirst({
        where: { id: userId, tenantId },
      });

      if (!user) {
        this.notFound('用户', userId);
      }

      // 检查虚拟币类型是否存在
      const currencyType = await (this.tenantPrisma as any).virtualCurrencyType.findFirst({
        where: { id: currencyTypeId, tenantId, isActive: true },
      });

      if (!currencyType) {
        this.notFound('虚拟币类型', currencyTypeId);
      }

      // 获取或创建用户钱包
      const wallet = await this.getOrCreateUserWallet(userId, currencyTypeId, tenantId);

      // 开始事务
      return await (this.tenantPrisma as any).$transaction(async (prisma: any) => {
        // 更新钱包余额
        const newBalance = parseFloat(wallet.balance) + amount;

        const updatedWallet = await prisma.userWallet.update({
          where: { id: wallet.id },
          data: {
            balance: newBalance,
            updateTime: new Date(),
          },
          include: {
            currencyType: true,
          },
        });

        // 创建交易记录
        const transaction = await prisma.currencyTransaction.create({
          data: {
            walletId: wallet.id,
            currencyTypeId,
            amount,
            balance: newBalance,
            type: TransactionType.PURCHASE,
            description: remark || `充值${currencyType.name}`,
            metadata: {
              ...metadata,
              orderNo,
            },
            tenantId,
            createTime: new Date(),
          },
        });

        return {
          wallet: {
            id: updatedWallet.id,
            userId,
            currencyTypeId,
            currencyCode: currencyType.code,
            currencyName: currencyType.name,
            balance: newBalance,
            icon: currencyType.icon,
            metadata: currencyType.metadata,
          },
          transaction: {
            id: transaction.id,
            amount,
            balance: newBalance,
            type: transaction.type,
            description: transaction.description,
            createTime: transaction.createTime,
          },
        };
      });
    } catch (error) {
      this.logError(
        `用户充值虚拟币失败，用户ID: ${rechargeDto.userId}, 币种ID: ${rechargeDto.currencyTypeId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * 用户消费虚拟币
   * @param consumeDto 消费数据
   * @param tenantId 租户ID
   * @returns 消费结果
   */
  async consumeUserWallet(consumeDto: ConsumeVirtualCurrencyDto, tenantId: string) {
    try {
      const { userId, currencyTypeId, amount, description, featureCode, metadata } = consumeDto;

      // 检查用户钱包
      const wallet = await (this.tenantPrisma as any).userWallet.findFirst({
        where: {
          userId,
          currencyTypeId,
          tenantId,
        },
        include: {
          currencyType: true,
        },
      });

      if (!wallet) {
        this.validationError('用户钱包不存在');
      }

      // 检查余额是否足够
      if (parseFloat(wallet.balance) < amount) {
        this.validationError('余额不足');
      }

      // 开始事务
      return await (this.tenantPrisma as any).$transaction(async (prisma: any) => {
        // 更新钱包余额
        const newBalance = parseFloat(wallet.balance) - amount;

        const updatedWallet = await prisma.userWallet.update({
          where: { id: wallet.id },
          data: {
            balance: newBalance,
            updateTime: new Date(),
          },
        });

        // 创建交易记录
        const transaction = await prisma.currencyTransaction.create({
          data: {
            walletId: wallet.id,
            currencyTypeId,
            amount: -amount, // 消费为负数
            balance: newBalance,
            type: TransactionType.CONSUME,
            description,
            metadata: {
              ...metadata,
              featureCode,
            },
            tenantId,
            createTime: new Date(),
          },
        });

        return {
          wallet: {
            id: updatedWallet.id,
            userId,
            currencyTypeId,
            currencyCode: wallet.currencyType.code,
            currencyName: wallet.currencyType.name,
            balance: newBalance,
            icon: wallet.currencyType.icon,
            metadata: wallet.currencyType.metadata,
          },
          transaction: {
            id: transaction.id,
            amount: -amount,
            balance: newBalance,
            type: transaction.type,
            description: transaction.description,
            createTime: transaction.createTime,
          },
        };
      });
    } catch (error) {
      this.logError(
        `用户消费虚拟币失败，用户ID: ${consumeDto.userId}, 币种ID: ${consumeDto.currencyTypeId}`,
        error,
      );
      throw error;
    }
  }

  /**
   * 查询用户钱包余额
   * @param queryDto 查询参数
   * @param tenantId 租户ID
   * @returns 钱包余额列表
   */
  async getUserWallets(queryDto: QueryUserWalletDto, tenantId: string) {
    try {
      const { userId, currencyTypeId } = queryDto;

      // 检查用户是否存在
      const user = await (this.tenantPrisma as any).user.findFirst({
        where: { id: userId, tenantId },
      });

      if (!user) {
        this.notFound('用户', userId);
      }

      // 构建查询条件
      const where: any = {
        userId,
        tenantId,
      };

      if (currencyTypeId) {
        where.currencyTypeId = currencyTypeId;
      }

      // 查询用户钱包
      const wallets = await (this.tenantPrisma as any).userWallet.findMany({
        where,
        include: {
          currencyType: true,
        },
      });

      // 格式化返回数据
      return wallets.map(wallet => ({
        id: wallet.id,
        userId: wallet.userId,
        currencyTypeId: wallet.currencyTypeId,
        currencyCode: wallet.currencyType.code,
        currencyName: wallet.currencyType.name,
        balance: parseFloat(wallet.balance),
        icon: wallet.currencyType.icon,
        metadata: wallet.currencyType.metadata,
      }));
    } catch (error) {
      this.logError(`查询用户钱包失败，用户ID: ${queryDto.userId}`, error);
      throw error;
    }
  }

  /**
   * 查询用户交易记录
   * @param queryDto 查询参数
   * @param options 分页选项
   * @param tenantId 租户ID
   * @returns 交易记录列表
   */
  async getUserTransactions(
    queryDto: QueryTransactionDto,
    options: PaginationOptions,
    tenantId: string,
  ) {
    try {
      const { userId, currencyTypeId, type, startTime, endTime } = queryDto;

      // 先获取用户的钱包IDs
      const walletWhere: any = {
        userId,
        tenantId,
      };

      if (currencyTypeId) {
        walletWhere.currencyTypeId = currencyTypeId;
      }

      const wallets = await (this.tenantPrisma as any).userWallet.findMany({
        where: walletWhere,
        select: { id: true },
      });

      if (wallets.length === 0) {
        return {
          items: [],
          total: 0,
          page: options.page,
          pageSize: options.pageSize,
          totalPages: 0,
        };
      }

      const walletIds = wallets.map(w => w.id);

      // 构建交易记录查询条件
      const where: any = {
        walletId: { in: walletIds },
        tenantId,
      };

      if (type) {
        where.type = type;
      }

      if (startTime) {
        where.createTime = {
          ...where.createTime,
          gte: new Date(startTime),
        };
      }

      if (endTime) {
        where.createTime = {
          ...where.createTime,
          lte: new Date(endTime),
        };
      }

      // 查询交易记录
      const [items, total] = await Promise.all([
        (this.tenantPrisma as any).currencyTransaction.findMany({
          where,
          include: {
            wallet: true,
            currencyType: true,
          },
          orderBy: { createTime: 'desc' },
          skip: options.skip,
          take: options.take,
        }),
        (this.tenantPrisma as any).currencyTransaction.count({ where }),
      ]);

      // 格式化返回数据
      const formattedItems = items.map(item => ({
        id: item.id,
        walletId: item.walletId,
        currencyTypeId: item.currencyTypeId,
        currencyCode: item.currencyType.code,
        currencyName: item.currencyType.name,
        amount: parseFloat(item.amount),
        balance: parseFloat(item.balance),
        type: item.type,
        description: item.description,
        metadata: item.metadata,
        createTime: item.createTime,
      }));

      return {
        items: formattedItems,
        total,
        page: options.page,
        pageSize: options.pageSize,
        totalPages: Math.ceil(total / options.pageSize),
      };
    } catch (error) {
      this.logError(`查询用户交易记录失败，用户ID: ${queryDto.userId}`, error);
      throw error;
    }
  }

  /**
   * 设置功能虚拟币消耗配置
   * @param featureCode 功能代码
   * @param costDto 消耗配置
   * @param tenantId 租户ID
   * @returns 设置结果
   */
  async setFeatureCurrencyCost(
    featureCode: string,
    costDto: FeatureCurrencyCostDto,
    tenantId: string,
  ) {
    try {
      // 检查虚拟币类型是否存在
      const currencyType = await (this.tenantPrisma as any).virtualCurrencyType.findFirst({
        where: { code: costDto.currencyCode, tenantId, isActive: true },
      });

      if (!currencyType) {
        this.validationError(`虚拟币类型 ${costDto.currencyCode} 不存在或未激活`);
      }

      // 在实际项目中，这里应该保存到一个专门的表中
      // 例如 feature_currency_cost 表
      // 这里简化处理，仅作为示例
      /*
      const featureCost = await (this.tenantPrisma as any).featureCurrencyCost.upsert({
        where: {
          featureCode_currencyCode: {
            featureCode,
            currencyCode: costDto.currencyCode,
          },
        },
        update: {
          cost: costDto.cost,
          memberDiscounts: costDto.memberDiscounts || {},
          bulkDiscounts: costDto.bulkDiscounts || [],
          isActive: costDto.isActive ?? true,
          updateTime: new Date(),
        },
        create: {
          featureCode,
          currencyCode: costDto.currencyCode,
          currencyTypeId: currencyType.id,
          cost: costDto.cost,
          memberDiscounts: costDto.memberDiscounts || {},
          bulkDiscounts: costDto.bulkDiscounts || [],
          isActive: costDto.isActive ?? true,
          tenantId,
          createTime: new Date(),
          updateTime: new Date(),
        },
      });

      return featureCost;
      */

      // 模拟返回
      return {
        featureCode,
        currencyCode: costDto.currencyCode,
        cost: costDto.cost,
        memberDiscounts: costDto.memberDiscounts || {},
        bulkDiscounts: costDto.bulkDiscounts || [],
        isActive: costDto.isActive ?? true,
      };
    } catch (error) {
      this.logError(`设置功能虚拟币消耗配置失败，功能: ${featureCode}`, error);
      throw error;
    }
  }

  /**
   * 获取或创建用户钱包
   * @param userId 用户ID
   * @param currencyTypeId 虚拟币类型ID
   * @param tenantId 租户ID
   * @returns 用户钱包
   */
  private async getOrCreateUserWallet(userId: number, currencyTypeId: number, tenantId: string) {
    try {
      // 查询用户钱包
      let wallet = await (this.tenantPrisma as any).userWallet.findFirst({
        where: {
          userId,
          currencyTypeId,
          tenantId,
        },
      });

      // 如果不存在，创建一个新钱包
      if (!wallet) {
        const now = new Date();
        wallet = await (this.tenantPrisma as any).userWallet.create({
          data: {
            userId,
            currencyTypeId,
            balance: 0,
            tenantId,
            createTime: now,
            updateTime: now,
          },
        });
      }

      return wallet;
    } catch (error) {
      this.logError(`获取或创建用户钱包失败，用户ID: ${userId}, 币种ID: ${currencyTypeId}`, error);
      throw error;
    }
  }

  /**
   * 检查代码是否存在
   * @param code 虚拟币代码
   * @param tenantId 租户ID
   * @returns 是否存在
   */
  private async isCodeExists(code: string, tenantId: string): Promise<boolean> {
    try {
      const count = await (this.tenantPrisma as any).virtualCurrencyType.count({
        where: {
          code,
          tenantId,
        },
      });
      return count > 0;
    } catch (error) {
      this.logError(`检查虚拟币代码是否存在失败，代码: ${code}`, error);
      return false;
    }
  }
}
