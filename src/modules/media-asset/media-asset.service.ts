import { Injectable, NotFoundException, BadRequestException, Inject } from '@nestjs/common';

import { CreateMediaAssetDto } from './dto/create-media-asset.dto';
import { QueryMediaAssetDto } from './dto/query-media-asset.dto';
import { UpdateMediaAssetDto } from './dto/update-media-asset.dto';
import { MediaAssetEntity } from './entities/media-asset.entity';

import { DatabaseFactory } from '@/core/database/database.factory';
import { BaseBusinessService } from '@/modules/business/base/business.service';

@Injectable()
export class MediaAssetService extends BaseBusinessService {
  constructor(databaseFactory: DatabaseFactory) {
    super(databaseFactory, 'MediaAssetService');
  }

  async create(
    tenantId: number,
    userId: number,
    createDto: CreateMediaAssetDto,
  ): Promise<MediaAssetEntity> {
    // 如果指定了网站ID，检查网站是否存在且属于当前租户
    if (createDto.websiteId) {
      const website = await this.tenantDb.website.findFirst({
        where: {
          id: createDto.websiteId,
          tenantId,
        },
      });

      if (!website) {
        throw new BadRequestException(`网站 ID ${createDto.websiteId} 不存在`);
      }
    }

    // 自动检测媒体分类（如果未指定）
    if (!createDto.category) {
      createDto.category = this.detectCategoryFromMimeType(createDto.mimeType);
    }

    const mediaAsset = await this.tenantDb.mediaAsset.create({
      data: {
        ...createDto,
        tenantId,
        userId,
      },
      include: {
        website: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
        user: {
          select: {
            id: true,
            username: true,
            realName: true,
          },
        },
      },
    });

    return mediaAsset;
  }

  async findAll(tenantId: number, userId?: number, queryDto: QueryMediaAssetDto = {}) {
    const {
      page = 1,
      limit = 20,
      keyword,
      category,
      mimeType,
      websiteId,
      userId: filterUserId,
      tags,
      sortBy = 'createTime',
      sortOrder = 'desc',
      includeRelations = false,
    } = queryDto;

    const skip = (page - 1) * limit;
    const take = limit;

    // 构建查询条件
    const where: any = {
      tenantId,
    };

    // 如果指定了用户ID（用于权限控制），只返回该用户上传的资源
    if (userId) {
      where.userId = userId;
    }

    if (keyword) {
      where.OR = [
        { filename: { contains: keyword, mode: 'insensitive' } },
        { originalName: { contains: keyword, mode: 'insensitive' } },
        { alt: { contains: keyword, mode: 'insensitive' } },
        { tags: { hasSome: [keyword] } },
      ];
    }

    if (category) {
      where.category = category;
    }

    if (mimeType) {
      where.mimeType = mimeType;
    }

    if (websiteId) {
      where.websiteId = websiteId;
    }

    if (filterUserId) {
      where.userId = filterUserId;
    }

    if (tags && tags.length > 0) {
      where.tags = { hasSome: tags };
    }

    // 构建排序条件
    const orderBy: any = {};
    orderBy[sortBy] = sortOrder;

    // 构建包含关系
    const include = includeRelations
      ? {
          website: {
            select: {
              id: true,
              name: true,
              status: true,
              subdomain: true,
              domain: true,
            },
          },
          user: {
            select: {
              id: true,
              username: true,
              realName: true,
            },
          },
        }
      : undefined;

    const [mediaAssets, total] = await Promise.all([
      this.tenantDb.mediaAsset.findMany({
        where,
        skip,
        take,
        orderBy,
        include,
      }),
      this.tenantDb.mediaAsset.count({ where }),
    ]);

    return {
      data: mediaAssets,
      total,
      page,
      limit,
      totalPages: Math.ceil(total / limit),
    };
  }

  async findOne(tenantId: number, id: number, userId?: number): Promise<MediaAssetEntity> {
    const where: any = {
      id,
      tenantId,
    };

    // 如果指定了用户ID（用于权限控制），只返回该用户上传的资源
    if (userId) {
      where.userId = userId;
    }

    const mediaAsset = await this.tenantDb.mediaAsset.findFirst({
      where,
      include: {
        website: {
          select: {
            id: true,
            name: true,
            status: true,
            subdomain: true,
            domain: true,
          },
        },
        user: {
          select: {
            id: true,
            username: true,
            realName: true,
          },
        },
      },
    });

    if (!mediaAsset) {
      throw new NotFoundException(`媒体资源 ID ${id} 不存在`);
    }

    return mediaAsset;
  }

  async update(
    tenantId: number,
    id: number,
    updateDto: UpdateMediaAssetDto,
    userId?: number,
  ): Promise<MediaAssetEntity> {
    await this.findOne(tenantId, id, userId); // 检查资源是否存在且有权限

    // 如果更新网站ID，检查网站是否存在且属于当前租户
    if (updateDto.websiteId) {
      const website = await this.tenantDb.website.findFirst({
        where: {
          id: updateDto.websiteId,
          tenantId,
        },
      });

      if (!website) {
        throw new BadRequestException(`网站 ID ${updateDto.websiteId} 不存在`);
      }
    }

    const where: any = {
      id,
      tenantId,
    };

    if (userId) {
      where.userId = userId;
    }

    const mediaAsset = await this.tenantDb.mediaAsset.update({
      where,
      data: updateDto,
      include: {
        website: {
          select: {
            id: true,
            name: true,
            status: true,
          },
        },
        user: {
          select: {
            id: true,
            username: true,
            realName: true,
          },
        },
      },
    });

    return mediaAsset;
  }

  async remove(tenantId: number, id: number, userId?: number): Promise<void> {
    await this.findOne(tenantId, id, userId); // 检查资源是否存在且有权限

    const where: any = {
      id,
      tenantId,
    };

    if (userId) {
      where.userId = userId;
    }

    await this.tenantDb.mediaAsset.delete({
      where,
    });
  }

  async getMediaByWebsite(tenantId: number, websiteId: number, category?: string) {
    const where: any = {
      websiteId,
      tenantId,
    };

    if (category) {
      where.category = category;
    }

    const mediaAssets = await this.tenantDb.mediaAsset.findMany({
      where,
      orderBy: [{ createTime: 'desc' }],
      select: {
        id: true,
        filename: true,
        originalName: true,
        url: true,
        thumbnailUrl: true,
        mimeType: true,
        category: true,
        size: true,
        alt: true,
        tags: true,
        createTime: true,
      },
    });

    return mediaAssets;
  }

  async getMediaStats(tenantId: number, websiteId?: number, userId?: number) {
    const where: any = {
      tenantId,
    };

    if (websiteId) {
      where.websiteId = websiteId;
    }

    if (userId) {
      where.userId = userId;
    }

    const [total, images, videos, documents, audio, totalSize] = await Promise.all([
      this.tenantDb.mediaAsset.count({ where }),
      this.tenantDb.mediaAsset.count({ where: { ...where, category: 'image' } }),
      this.tenantDb.mediaAsset.count({ where: { ...where, category: 'video' } }),
      this.tenantDb.mediaAsset.count({ where: { ...where, category: 'document' } }),
      this.tenantDb.mediaAsset.count({ where: { ...where, category: 'audio' } }),
      this.tenantDb.mediaAsset.aggregate({
        where,
        _sum: {
          size: true,
        },
      }),
    ]);

    return {
      total,
      byCategory: {
        images,
        videos,
        documents,
        audio,
      },
      totalSize: totalSize._sum.size || 0,
    };
  }

  async getCategories(tenantId: number): Promise<string[]> {
    const categories = await this.tenantDb.mediaAsset.findMany({
      where: {
        tenantId,
      },
      select: {
        category: true,
      },
      distinct: ['category'],
    });

    return categories.map(item => item.category);
  }

  async getTags(tenantId: number): Promise<string[]> {
    const mediaAssets = await this.tenantDb.mediaAsset.findMany({
      where: {
        tenantId,
      },
      select: {
        tags: true,
      },
    });

    // 提取所有标签并去重
    const allTags = mediaAssets.flatMap(asset => asset.tags);
    return [...new Set(allTags)];
  }

  async moveToWebsite(
    tenantId: number,
    mediaIds: number[],
    websiteId: number,
    userId?: number,
  ): Promise<void> {
    // 检查网站是否存在且属于当前租户
    const website = await this.tenantDb.website.findFirst({
      where: {
        id: websiteId,
        tenantId,
      },
    });

    if (!website) {
      throw new BadRequestException(`网站 ID ${websiteId} 不存在`);
    }

    const where: any = {
      id: { in: mediaIds },
      tenantId,
    };

    if (userId) {
      where.userId = userId;
    }

    await this.tenantDb.mediaAsset.updateMany({
      where,
      data: {
        websiteId,
      },
    });
  }

  async addTags(
    tenantId: number,
    id: number,
    tags: string[],
    userId?: number,
  ): Promise<MediaAssetEntity> {
    const mediaAsset = await this.findOne(tenantId, id, userId);

    // 合并现有标签和新标签，去重
    const existingTags = mediaAsset.tags || [];
    const newTags = [...new Set([...existingTags, ...tags])];

    return this.update(tenantId, id, { tags: newTags }, userId);
  }

  async removeTags(
    tenantId: number,
    id: number,
    tags: string[],
    userId?: number,
  ): Promise<MediaAssetEntity> {
    const mediaAsset = await this.findOne(tenantId, id, userId);

    // 从现有标签中移除指定标签
    const existingTags = mediaAsset.tags || [];
    const newTags = existingTags.filter(tag => !tags.includes(tag));

    return this.update(tenantId, id, { tags: newTags }, userId);
  }

  private detectCategoryFromMimeType(mimeType: string): string {
    if (mimeType.startsWith('image/')) {
      return 'image';
    } else if (mimeType.startsWith('video/')) {
      return 'video';
    } else if (mimeType.startsWith('audio/')) {
      return 'audio';
    } else {
      return 'document';
    }
  }

  // TODO: 实现媒体资产功能
  async getAssets() {
    throw new Error('Method not implemented - Schema needs to be verified');
  }
}
