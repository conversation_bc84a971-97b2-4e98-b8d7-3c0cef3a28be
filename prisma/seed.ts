import { PrismaClient } from '@prisma/client';
import * as bcryptjs from 'bcryptjs';

const prisma = new PrismaClient();

async function main() {
  console.log('🌱 开始执行种子数据初始化...');

  // ================================
  // 1. 创建订阅计划
  // ================================
  console.log('📦 创建订阅计划...');

  const subscriptionPlans = [
    {
      code: 'free',
      name: '免费版',
      description: '适合个人用户试用，体验基础功能',
      price: 0,
      currency: 'CNY',
      billingCycle: 'monthly',
      features: ['1个网站', '基础模板', '社区支持', '基础分析', '5GB存储'],
      limits: {
        websites: 1,
        storage: 5120, // MB
        bandwidth: 10240, // MB
        users: 1,
        domains: 0,
        api_calls: 1000,
      },
      sortOrder: 1,
      isActive: true,
    },
    {
      code: 'basic',
      name: '基础版',
      description: '适合小团队使用，包含基础商业功能',
      price: 99,
      currency: 'CNY',
      billingCycle: 'monthly',
      features: ['3个网站', '自定义域名', '邮件支持', '基础SEO工具', '移除水印', '50GB存储'],
      limits: {
        websites: 3,
        storage: 51200,
        bandwidth: 102400,
        users: 5,
        domains: 1,
        api_calls: 10000,
      },
      sortOrder: 2,
      isActive: true,
    },
    {
      code: 'professional',
      name: '专业版',
      description: '适合中小企业，包含高级功能',
      price: 299,
      currency: 'CNY',
      billingCycle: 'monthly',
      features: [
        '10个网站',
        '高级模板',
        '电商功能',
        '优先支持',
        '高级分析',
        'API访问',
        '200GB存储',
      ],
      limits: {
        websites: 10,
        storage: 204800,
        bandwidth: 1048576,
        users: 50,
        domains: 5,
        api_calls: 100000,
      },
      sortOrder: 3,
      isActive: true,
    },
    {
      code: 'enterprise',
      name: '企业版',
      description: '适合大型企业，包含企业级功能',
      price: 999,
      currency: 'CNY',
      billingCycle: 'monthly',
      features: [
        '无限网站',
        '企业级模板',
        '完整电商套件',
        '专属客户经理',
        '高级安全',
        '完整API',
        '无限存储',
        '自定义集成',
      ],
      limits: {
        websites: -1, // -1 表示无限制
        storage: -1,
        bandwidth: -1,
        users: -1,
        domains: -1,
        api_calls: -1,
      },
      sortOrder: 4,
      isActive: true,
    },
  ];

  for (const plan of subscriptionPlans) {
    await prisma.subscriptionPlan.upsert({
      where: { code: plan.code },
      update: {
        name: plan.name,
        description: plan.description,
        price: plan.price,
        features: plan.features,
        limits: plan.limits,
        sortOrder: plan.sortOrder,
        isActive: plan.isActive,
        updatedAt: new Date(),
      },
      create: plan,
    });
  }

  console.log('✅ 订阅计划创建完成');

  // ================================
  // 2. 创建系统级角色
  // ================================
  console.log('👥 创建系统级角色...');

  const systemRoles = [
    {
      code: 'SUPER_ADMIN',
      name: '超级管理员',
      description: '系统最高权限，可以管理所有功能',
      permissions: [
        'SYSTEM_ADMIN',
        'TENANT_MANAGE',
        'USER_MANAGE',
        'SUBSCRIPTION_MANAGE',
        'SYSTEM_CONFIG',
        'AUDIT_LOG',
        'PLATFORM_ANALYTICS',
      ],
      isSystem: true,
      tenantId: null,
    },
    {
      code: 'SYSTEM_ADMIN',
      name: '系统管理员',
      description: '系统管理权限，可以管理租户和用户',
      permissions: [
        'TENANT_MANAGE',
        'USER_MANAGE',
        'SUBSCRIPTION_MANAGE',
        'AUDIT_LOG',
        'PLATFORM_ANALYTICS',
      ],
      isSystem: true,
      tenantId: null,
    },
    {
      code: 'SYSTEM_OPERATOR',
      name: '系统运营',
      description: '系统运营权限，可以查看统计和日志',
      permissions: ['TENANT_VIEW', 'USER_VIEW', 'AUDIT_LOG', 'PLATFORM_ANALYTICS'],
      isSystem: true,
      tenantId: null,
    },
  ];

  for (const role of systemRoles) {
    await prisma.role.upsert({
      where: {
        tenantId_code: {
          tenantId: null,
          code: role.code,
        },
      },
      update: {
        name: role.name,
        remark: role.description,
        permissions: role.permissions,
        updatedAt: new Date(),
      },
      create: {
        ...role,
        remark: role.description,
      },
    });
  }

  console.log('✅ 系统级角色创建完成');

  // ================================
  // 3. 创建系统官网租户
  // ================================
  console.log('🌐 创建系统官网租户...');

  const systemTenant = await prisma.tenant.upsert({
    where: { id: 0 },
    update: {
      name: 'FlexiHub官网',
      domain: 'www.flexihub.com',
      metadata: {
        type: 'system',
        purpose: 'official_website',
        unlimited: true,
        features: ['unlimited_all'],
      },
      updateTime: new Date(),
    },
    create: {
      id: 0,
      code: 'flexihub-official',
      name: 'FlexiHub官网',
      website: 'https://www.flexihub.com',
      domain: 'www.flexihub.com',
      status: 1,
      registrationSource: 'system',
      metadata: {
        type: 'system',
        purpose: 'official_website',
        unlimited: true,
        features: ['unlimited_all'],
      },
    },
  });

  console.log('✅ 系统官网租户创建完成');

  // ================================
  // 4. 创建系统管理员用户
  // ================================
  console.log('👤 创建系统管理员用户...');

  const hashedPassword = await bcryptjs.hash('FlexiHub2024!', 10);

  const superAdmin = await prisma.user.upsert({
    where: {
      username_tenantId: {
        tenantId: 0,
        username: 'admin',
      },
    },
    update: {
      password: hashedPassword,
      emailAddress: '<EMAIL>',
      realName: '系统管理员',
      userType: 'SYSTEM',
      updatedAt: new Date(),
    },
    create: {
      tenantId: 0,
      username: 'admin',
      password: hashedPassword,
      emailAddress: '<EMAIL>',
      realName: '系统管理员',
      phoneNumber: '13800138000',
      avatar: null,
      status: 1,
      userType: 'SYSTEM',
      metadata: {
        role: 'super_admin',
        created_by: 'system',
        is_default: true,
      },
    },
  });

  // 分配超级管理员角色
  const superAdminRole = await prisma.role.findFirst({
    where: {
      code: 'SUPER_ADMIN',
      isSystem: true,
    },
  });

  if (superAdminRole) {
    await prisma.userRole.upsert({
      where: {
        userId_roleId: {
          userId: superAdmin.id,
          roleId: superAdminRole.id,
        },
      },
      update: {},
      create: {
        userId: superAdmin.id,
        roleId: superAdminRole.id,
        tenantId: 0,
      },
    });
  }

  console.log('✅ 系统管理员用户创建完成');

  // ================================
  // 5. 创建系统官网
  // ================================
  console.log('🏠 创建系统官网...');

  const officialWebsite = await prisma.website.upsert({
    where: { id: 0 },
    update: {
      name: 'FlexiHub官网',
      domain: 'www.flexihub.com',
      status: 'published',
      updatedAt: new Date(),
    },
    create: {
      id: 0,
      tenantId: 0,
      name: 'FlexiHub官网',
      domain: 'www.flexihub.com',
      status: 'published',
      seoConfig: {
        title: 'FlexiHub - 企业级多租户建站平台',
        description: '专业的SaaS建站解决方案，5分钟快速搭建企业网站',
        keywords: '建站,SaaS,多租户,企业级,网站建设,在线建站',
      },
      settings: {
        performance: {
          cache: true,
          cdn: true,
          compression: true,
          preload: true,
        },
        features: {
          unlimited: true,
          priority: 'highest',
          analytics: true,
          sitemap: true,
        },
        theme: {
          primary_color: '#2563eb',
          secondary_color: '#1e40af',
          accent_color: '#3b82f6',
          font_family: 'Inter, -apple-system, BlinkMacSystemFont',
        },
      },
      userId: superAdmin.id,
      publishedAt: new Date(),
    },
  });

  console.log('✅ 系统官网创建完成');

  // ================================
  // 6. 创建系统配置
  // ================================
  console.log('⚙️ 创建系统配置...');

  const systemConfigs = [
    {
      category: 'registration',
      key: 'enable_self_registration',
      value: 'true',
      dataType: 'boolean',
      description: '是否启用自助注册',
      isPublic: false,
    },
    {
      category: 'registration',
      key: 'default_plan',
      value: 'free',
      dataType: 'string',
      description: '默认订阅计划',
      isPublic: false,
    },
    {
      category: 'registration',
      key: 'require_email_verification',
      value: 'true',
      dataType: 'boolean',
      description: '是否需要邮箱验证',
      isPublic: false,
    },
    {
      category: 'system',
      key: 'maintenance_mode',
      value: 'false',
      dataType: 'boolean',
      description: '系统维护模式',
      isPublic: true,
    },
    {
      category: 'system',
      key: 'system_name',
      value: 'FlexiHub',
      dataType: 'string',
      description: '系统名称',
      isPublic: true,
    },
    {
      category: 'system',
      key: 'system_version',
      value: '2.0.0',
      dataType: 'string',
      description: '系统版本',
      isPublic: true,
    },
    {
      category: 'email',
      key: 'from_name',
      value: 'FlexiHub',
      dataType: 'string',
      description: '邮件发送者名称',
      isPublic: false,
    },
    {
      category: 'email',
      key: 'from_email',
      value: '<EMAIL>',
      dataType: 'string',
      description: '邮件发送者地址',
      isPublic: false,
    },
    {
      category: 'analytics',
      key: 'enable_tracking',
      value: 'true',
      dataType: 'boolean',
      description: '是否启用使用统计',
      isPublic: false,
    },
    {
      category: 'features',
      key: 'max_websites_per_tenant',
      value: '50',
      dataType: 'number',
      description: '每个租户最大网站数量',
      isPublic: false,
    },
  ];

  for (const config of systemConfigs) {
    await prisma.systemConfig.upsert({
      where: {
        category_key: {
          category: config.category,
          key: config.key,
        },
      },
      update: {
        value: config.value,
        dataType: config.dataType,
        description: config.description,
        isPublic: config.isPublic,
        updatedAt: new Date(),
      },
      create: config,
    });
  }

  console.log('✅ 系统配置创建完成');

  // ================================
  // 7. 为现有租户分配默认订阅
  // ================================
  console.log('🔄 为现有租户分配默认订阅...');

  const existingTenants = await prisma.tenant.findMany({
    where: {
      id: { gt: 0 }, // 排除系统租户
      subscriptions: { none: {} }, // 没有订阅的租户
    },
  });

  const freePlan = await prisma.subscriptionPlan.findFirst({
    where: { code: 'free' },
  });

  if (freePlan && existingTenants.length > 0) {
    for (const tenant of existingTenants) {
      const startDate = new Date();
      const endDate = new Date();
      endDate.setFullYear(endDate.getFullYear() + 1); // 1年有效期

      await prisma.tenantSubscription.create({
        data: {
          tenantId: tenant.id,
          planId: freePlan.id,
          duration: 12,
          billingCycle: 'yearly',
          startDate,
          endDate,
          status: 'active',
          metadata: {
            source: 'migration',
            note: 'Default subscription for existing tenant',
            auto_assigned: true,
          },
        },
      });

      // 添加基础功能权限
      const basicFeatures = [
        { code: 'basic_website', quota: 1 },
        { code: 'storage', quota: 5120 }, // 5GB
        { code: 'bandwidth', quota: 10240 }, // 10GB
        { code: 'api_calls', quota: 1000 },
      ];

      for (const feature of basicFeatures) {
        await prisma.tenantFeature.upsert({
          where: {
            tenantId_featureCode: {
              tenantId: tenant.id,
              featureCode: feature.code,
            },
          },
          update: {},
          create: {
            tenantId: tenant.id,
            featureCode: feature.code,
            enabled: true,
            quota: feature.quota,
            usedQuota: 0,
          },
        });
      }

      // 添加基础配置
      const basicConfigs = [
        { category: 'website', key: 'default_theme', value: 'modern' },
        { category: 'system', key: 'language', value: 'zh-CN' },
        { category: 'system', key: 'timezone', value: 'Asia/Shanghai' },
      ];

      for (const config of basicConfigs) {
        await prisma.tenantConfig.upsert({
          where: {
            tenantId_category_key: {
              tenantId: tenant.id,
              category: config.category,
              key: config.key,
            },
          },
          update: {},
          create: {
            tenantId: tenant.id,
            category: config.category,
            key: config.key,
            value: config.value,
            dataType: 'string',
          },
        });
      }
    }

    console.log(`✅ 为 ${existingTenants.length} 个现有租户分配了默认订阅`);
  }

  // ================================
  // 8. 输出统计信息
  // ================================
  const stats = {
    tenants: await prisma.tenant.count(),
    subscriptionPlans: await prisma.subscriptionPlan.count(),
    subscriptions: await prisma.tenantSubscription.count(),
    users: await prisma.user.count(),
    websites: await prisma.website.count(),
    systemConfigs: await prisma.systemConfig.count(),
  };

  console.log('📊 种子数据初始化完成统计:');
  console.log(`   - 租户总数: ${stats.tenants}`);
  console.log(`   - 订阅计划: ${stats.subscriptionPlans}`);
  console.log(`   - 订阅记录: ${stats.subscriptions}`);
  console.log(`   - 用户总数: ${stats.users}`);
  console.log(`   - 网站总数: ${stats.websites}`);
  console.log(`   - 系统配置: ${stats.systemConfigs}`);

  console.log('🎉 FlexiHub企业级多租户系统种子数据初始化完成！');
  console.log('💡 默认管理员账户:');
  console.log('   - 用户名: admin');
  console.log('   - 密码: FlexiHub2024!');
  console.log('   - 邮箱: <EMAIL>');
}

main()
  .then(async () => {
    await prisma.$disconnect();
  })
  .catch(async e => {
    console.error('❌ 种子数据初始化失败:', e);
    await prisma.$disconnect();
    process.exit(1);
  });
