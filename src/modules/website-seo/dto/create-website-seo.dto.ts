import { ApiProperty } from '@nestjs/swagger';
import { JsonValue } from '@prisma/client/runtime/library';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsInt, IsObject, Min } from 'class-validator';

export class CreateWebsiteSeoDto {
  @ApiProperty({ description: '所属网站ID' })
  @IsInt()
  @Min(1)
  @Type(() => Number)
  websiteId: number;

  @ApiProperty({ description: '网站标题', required: false })
  @IsOptional()
  @IsString()
  title?: string;

  @ApiProperty({ description: '网站描述', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '关键词', required: false })
  @IsOptional()
  @IsString()
  keywords?: string;

  @ApiProperty({ description: '作者', required: false })
  @IsOptional()
  @IsString()
  author?: string;

  @ApiProperty({ description: 'robots指令', default: 'index,follow' })
  @IsOptional()
  @IsString()
  robots?: string = 'index,follow';

  @ApiProperty({ description: '规范URL', required: false })
  @IsOptional()
  @IsString()
  canonical?: string;

  @ApiProperty({ description: 'Open Graph标题', required: false })
  @IsOptional()
  @IsString()
  ogTitle?: string;

  @ApiProperty({ description: 'Open Graph描述', required: false })
  @IsOptional()
  @IsString()
  ogDescription?: string;

  @ApiProperty({ description: 'Open Graph图片', required: false })
  @IsOptional()
  @IsString()
  ogImage?: string;

  @ApiProperty({ description: 'Open Graph类型', default: 'website' })
  @IsOptional()
  @IsString()
  ogType?: string = 'website';

  @ApiProperty({ description: 'Twitter卡片类型', default: 'summary' })
  @IsOptional()
  @IsString()
  twitterCard?: string = 'summary';

  @ApiProperty({ description: 'Twitter站点', required: false })
  @IsOptional()
  @IsString()
  twitterSite?: string;

  @ApiProperty({ description: 'Twitter创建者', required: false })
  @IsOptional()
  @IsString()
  twitterCreator?: string;

  @ApiProperty({
    description: '结构化数据',
    type: 'object',
    required: false,
    example: {
      '@context': 'https://schema.org',
      '@type': 'Organization',
      name: '公司名称',
      url: 'https://example.com',
      logo: 'https://example.com/logo.png',
    },
  })
  @IsOptional()
  @IsObject()
  structuredData?: JsonValue;

  @ApiProperty({
    description: '自定义meta标签',
    type: 'object',
    required: false,
    example: {
      'theme-color': '#000000',
      'apple-mobile-web-app-capable': 'yes',
      'apple-mobile-web-app-status-bar-style': 'black',
    },
  })
  @IsOptional()
  @IsObject()
  customMeta?: JsonValue;

  @ApiProperty({
    description: '分析工具配置',
    type: 'object',
    required: false,
    example: {
      googleAnalytics: 'GA_MEASUREMENT_ID',
      baiduAnalytics: 'BAIDU_SITE_ID',
      googleTagManager: 'GTM_ID',
    },
  })
  @IsOptional()
  @IsObject()
  analytics?: JsonValue;

  @ApiProperty({
    description: '网站验证码',
    type: 'object',
    required: false,
    example: {
      google: 'google-site-verification-code',
      baidu: 'baidu-site-verification-code',
      bing: 'bing-site-verification-code',
    },
  })
  @IsOptional()
  @IsObject()
  verification?: JsonValue;
}
