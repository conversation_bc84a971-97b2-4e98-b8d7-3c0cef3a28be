import { ApiProperty } from '@nestjs/swagger';
import { JsonValue } from '@prisma/client/runtime/library';
import { Type } from 'class-transformer';
import { IsString, IsOptional, IsBoolean, IsInt, IsObject, IsDate } from 'class-validator';

export class WebsiteEntity {
  @ApiProperty({ description: '网站ID' })
  id: number;

  @ApiProperty({ description: '网站名称' })
  @IsString()
  name: string;

  @ApiProperty({ description: '网站描述', required: false })
  @IsOptional()
  @IsString()
  description?: string;

  @ApiProperty({ description: '自定义域名', required: false })
  @IsOptional()
  @IsString()
  domain?: string;

  @ApiProperty({ description: '子域名', required: false })
  @IsOptional()
  @IsString()
  subdomain?: string;

  @ApiProperty({ description: '使用的模板ID', required: false })
  @IsOptional()
  @IsInt()
  templateId?: number;

  @ApiProperty({ description: '网站图标', required: false })
  @IsOptional()
  @IsString()
  favicon?: string;

  @ApiProperty({ description: '网站Logo', required: false })
  @IsOptional()
  @IsString()
  logo?: string;

  @ApiProperty({
    description: '网站状态',
    enum: ['draft', 'published', 'archived'],
    default: 'draft',
  })
  @IsString()
  status: string;

  @ApiProperty({ description: 'SEO配置', type: 'object', required: false })
  @IsOptional()
  @IsObject()
  seoConfig?: JsonValue;

  @ApiProperty({ description: '主题配置', type: 'object', required: false })
  @IsOptional()
  @IsObject()
  themeConfig?: JsonValue;

  @ApiProperty({ description: '网站设置', type: 'object', required: false })
  @IsOptional()
  @IsObject()
  settings?: JsonValue;

  @ApiProperty({ description: '元数据', type: 'object', required: false })
  @IsOptional()
  @IsObject()
  metadata?: JsonValue;

  @ApiProperty({ description: '发布时间', required: false })
  @IsOptional()
  @IsDate()
  @Type(() => Date)
  publishedAt?: Date;

  @ApiProperty({ description: '租户ID' })
  tenantId: number;

  @ApiProperty({ description: '创建者ID' })
  userId: number;

  @ApiProperty({ description: '创建时间' })
  @IsDate()
  @Type(() => Date)
  createTime: Date;

  @ApiProperty({ description: '更新时间' })
  @IsDate()
  @Type(() => Date)
  updateTime: Date;

  // 关联对象（可选，用于返回详细信息）
  @ApiProperty({ description: '使用的模板', required: false })
  @IsOptional()
  template?: any;

  @ApiProperty({ description: '创建者信息', required: false })
  @IsOptional()
  user?: any;

  @ApiProperty({ description: '页面数量', required: false })
  @IsOptional()
  @IsInt()
  pageCount?: number;

  @ApiProperty({ description: '媒体资源数量', required: false })
  @IsOptional()
  @IsInt()
  mediaCount?: number;
}
