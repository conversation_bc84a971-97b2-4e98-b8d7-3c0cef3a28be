import { Injectable, Logger } from '@nestjs/common';
import { Cron } from '@nestjs/schedule';
import { PrismaClient } from '@prisma/client';

import { DatabaseFactory } from '@/core/database/database.factory';
import { DatabaseFactory } from '@/core/database/database.factory';
import { TenantService } from '@/modules/tenant/tenant.service';

/**
 * 会员任务服务
 * 处理会员相关的定时任务
 */
@Injectable()
export class MembershipTasksService {
  private readonly logger = new Logger(MembershipTasksService.name);

  constructor(
    private readonly publicPrisma: PublicPrismaService,
    private readonly tenantService: TenantService,
  ) {}

  /**
   * 处理过期会员
   * 每天凌晨2点执行
   */
  @Cron('0 0 2 * * *')
  async handleExpiredMemberships() {
    this.logger.log('开始处理过期会员...');

    try {
      // 获取所有租户
      const tenants = await this.publicPrisma.tenant.findMany({
        where: {
          status: 1, // 1-启用
        },
        select: {
          id: true,
          name: true,
          datasource: {
            select: {
              url: true,
            },
          },
        },
      });

      this.logger.log(`找到 ${tenants.length} 个活跃租户`);

      // 为每个租户处理过期会员
      for (const tenant of tenants) {
        await this.handleExpiredMembershipsForTenant({
          id: tenant.id.toString(),
          name: tenant.name,
          databaseUrl: tenant.datasource?.url || '',
        });
      }

      this.logger.log('过期会员处理完成');
    } catch (error) {
      this.logger.error('处理过期会员时出错:', error.stack);
    }
  }

  /**
   * 处理即将过期的会员提醒
   * 每天早上9点执行
   */
  @Cron('0 0 9 * * *')
  async handleExpirationReminders() {
    this.logger.log('开始处理会员到期提醒...');

    try {
      // 获取所有租户
      const tenants = await this.publicPrisma.tenant.findMany({
        where: {
          status: 1, // 1-启用
        },
        select: {
          id: true,
          name: true,
          datasource: {
            select: {
              url: true,
            },
          },
        },
      });

      this.logger.log(`找到 ${tenants.length} 个活跃租户`);

      // 为每个租户处理即将过期的会员提醒
      for (const tenant of tenants) {
        await this.handleExpirationRemindersForTenant({
          id: tenant.id.toString(),
          name: tenant.name,
          databaseUrl: tenant.datasource?.url || '',
        });
      }

      this.logger.log('会员到期提醒处理完成');
    } catch (error) {
      this.logger.error('处理会员到期提醒时出错:', error.stack);
    }
  }

  /**
   * 处理自动续费
   * 每天凌晨3点执行
   */
  @Cron('0 0 3 * * *')
  async handleAutoRenewals() {
    this.logger.log('开始处理会员自动续费...');

    try {
      // 获取所有租户
      const tenants = await this.publicPrisma.tenant.findMany({
        where: {
          status: 1, // 1-启用
        },
        select: {
          id: true,
          name: true,
          datasource: {
            select: {
              url: true,
            },
          },
        },
      });

      this.logger.log(`找到 ${tenants.length} 个活跃租户`);

      // 为每个租户处理自动续费
      for (const tenant of tenants) {
        await this.handleAutoRenewalsForTenant({
          id: tenant.id.toString(),
          name: tenant.name,
          databaseUrl: tenant.datasource?.url || '',
        });
      }

      this.logger.log('会员自动续费处理完成');
    } catch (error) {
      this.logger.error('处理会员自动续费时出错:', error.stack);
    }
  }

  /**
   * 处理租户的过期会员
   * @param tenant 租户信息
   */
  private async handleExpiredMembershipsForTenant(tenant: {
    id: string;
    name: string;
    databaseUrl: string;
  }) {
    this.logger.log(`处理租户 ${tenant.name} (${tenant.id}) 的过期会员`);

    try {
      // 获取租户特定的Prisma客户端
      const tenantPrisma = new TenantPrismaService(tenant.databaseUrl).withQueryExtensions(
        parseInt(tenant.id),
      );

      // 获取当前日期
      const now = new Date();

      // 查找已过期但状态仍为活跃的会员
      const expiredMemberships = await (tenantPrisma as any).userMembership.findMany({
        where: {
          status: 'active',
          endDate: {
            lt: now,
          },
        },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              realName: true,
            },
          },
          plan: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
        },
      });

      this.logger.log(`找到 ${expiredMemberships.length} 个过期会员`);

      // 更新过期会员状态
      for (const membership of expiredMemberships) {
        await (tenantPrisma as any).userMembership.update({
          where: {
            id: membership.id,
          },
          data: {
            status: 'expired',
            updateTime: now,
          },
        });

        this.logger.log(
          `已将会员 ${membership.id} (用户: ${membership.user.realName || membership.user.username}, 计划: ${membership.plan.name}) 标记为过期`,
        );

        // 在实际项目中，这里可以发送过期通知、保存事件日志等
      }
    } catch (error) {
      this.logger.error(`处理租户 ${tenant.name} 的过期会员时出错:`, error.stack);
    }
  }

  /**
   * 处理租户的即将过期会员提醒
   * @param tenant 租户信息
   */
  private async handleExpirationRemindersForTenant(tenant: {
    id: string;
    name: string;
    databaseUrl: string;
  }) {
    this.logger.log(`处理租户 ${tenant.name} (${tenant.id}) 的会员到期提醒`);

    try {
      // 获取租户特定的Prisma客户端
      const tenantPrisma = new TenantPrismaService(tenant.databaseUrl).withQueryExtensions(
        parseInt(tenant.id),
      );

      // 获取当前日期和7天后的日期
      const now = new Date();
      const sevenDaysLater = new Date();
      sevenDaysLater.setDate(now.getDate() + 7);

      // 查找即将在7天内过期的活跃会员
      const soonToExpireMemberships = await (tenantPrisma as any).userMembership.findMany({
        where: {
          status: 'active',
          endDate: {
            gte: now,
            lte: sevenDaysLater,
          },
        },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              realName: true,
              emailAddress: true,
            },
          },
          plan: {
            select: {
              id: true,
              name: true,
              code: true,
            },
          },
        },
      });

      this.logger.log(`找到 ${soonToExpireMemberships.length} 个即将过期的会员`);

      // 发送提醒通知
      for (const membership of soonToExpireMemberships) {
        const daysRemaining = Math.ceil(
          (membership.endDate.getTime() - now.getTime()) / (1000 * 60 * 60 * 24),
        );

        this.logger.log(
          `准备发送提醒给用户 ${membership.user.realName || membership.user.username}，会员将在 ${daysRemaining} 天后过期`,
        );

        // 在实际项目中，这里可以发送邮件、短信、站内信等提醒
        // await this.notificationService.sendExpirationReminder(membership);
      }
    } catch (error) {
      this.logger.error(`处理租户 ${tenant.name} 的会员到期提醒时出错:`, error.stack);
    }
  }

  /**
   * 处理租户的会员自动续费
   * @param tenant 租户信息
   */
  private async handleAutoRenewalsForTenant(tenant: {
    id: string;
    name: string;
    databaseUrl: string;
  }) {
    this.logger.log(`处理租户 ${tenant.name} (${tenant.id}) 的会员自动续费`);

    try {
      // 获取租户特定的Prisma客户端
      const tenantPrisma = new TenantPrismaService(tenant.databaseUrl).withQueryExtensions(
        parseInt(tenant.id),
      );

      // 获取当前日期和明天的日期
      const now = new Date();
      const tomorrow = new Date();
      tomorrow.setDate(now.getDate() + 1);

      // 查找明天到期且开启自动续费的活跃会员
      const autoRenewMemberships = await (tenantPrisma as any).userMembership.findMany({
        where: {
          status: 'active',
          autoRenew: true,
          endDate: {
            gte: now,
            lt: tomorrow,
          },
        },
        include: {
          user: {
            select: {
              id: true,
              username: true,
              realName: true,
            },
          },
          plan: {
            select: {
              id: true,
              name: true,
              code: true,
              price: true,
              billingCycle: true,
            },
          },
        },
      });

      this.logger.log(`找到 ${autoRenewMemberships.length} 个需要自动续费的会员`);

      // 处理自动续费
      for (const membership of autoRenewMemberships) {
        this.logger.log(
          `准备为用户 ${membership.user.realName || membership.user.username} 续费 ${membership.plan.name} 会员`,
        );

        try {
          // 在实际项目中，这里需要调用支付服务进行扣费
          // const paymentResult = await this.paymentService.processAutoRenewal(membership);

          // 模拟支付成功
          const paymentResult = { success: true };

          if (paymentResult.success) {
            // 计算新的到期日期
            const currentEndDate = new Date(membership.endDate);
            const newEndDate = new Date(currentEndDate);

            // 根据计费周期延长时间
            switch (membership.plan.billingCycle) {
              case 'monthly':
                newEndDate.setMonth(newEndDate.getMonth() + 1);
                break;
              case 'quarterly':
                newEndDate.setMonth(newEndDate.getMonth() + 3);
                break;
              case 'yearly':
                newEndDate.setFullYear(newEndDate.getFullYear() + 1);
                break;
              default:
                newEndDate.setMonth(newEndDate.getMonth() + 1); // 默认按月续费
            }

            // 更新会员到期时间
            await (tenantPrisma as any).userMembership.update({
              where: {
                id: membership.id,
              },
              data: {
                endDate: newEndDate,
                updateTime: now,
              },
            });

            this.logger.log(
              `已为用户 ${membership.user.realName || membership.user.username} 续费，新的到期日期为 ${newEndDate.toISOString()}`,
            );

            // 在实际项目中，这里可以记录续费日志、发送续费成功通知等
          } else {
            this.logger.warn(`用户 ${membership.user.username} 自动续费失败，需要手动处理`);
            // 可以发送续费失败通知，提醒用户手动续费
          }
        } catch (renewalError) {
          this.logger.error(
            `处理用户 ${membership.user.username} 的自动续费时出错:`,
            renewalError.stack,
          );
        }
      }
    } catch (error) {
      this.logger.error(`处理租户 ${tenant.name} 的会员自动续费时出错:`, error.stack);
    }
  }
}
